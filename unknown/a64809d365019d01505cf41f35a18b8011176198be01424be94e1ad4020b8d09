<?php
if (!defined('ABSPATH')) {
    exit;
}

class QKB_OpenAI_Completion extends QKB_OpenAI_Base {
    private $ml_patterns = [];
    private $prompts_handler;
    private $context_handler;

    public function __construct() {
        parent::__construct();
        $this->prompts_handler = new QKB_OpenAI_Prompts();
        $this->context_handler = new QKB_OpenAI_Context();
    }

    private function handle_error($message, $exception) {
        if ($this->error_handler && method_exists($this->error_handler, 'log_error')) {
            $this->error_handler->log_error($message, $exception);
        } else {
            error_log("QKB Error: {$message}");
            if ($exception) {
                error_log("Exception: " . $exception->getMessage());
            }
        }

        $error_message = 'An error occurred while processing your request.';

        if (strpos($exception->getMessage(), 'cURL error 28') !== false) {
            $error_message = 'The request timed out. Please try again or rephrase your question.';
        } elseif (strpos($exception->getMessage(), 'No knowledge base categories found') !== false) {
            $error_message = 'The knowledge base is still being set up. Please try again later.';
        }

        return new WP_Error('completion_error', $error_message);
    }

    public function get_completion($message, $context = '', $assistant_id = null, $conversation_context = []) {
        if (!$this->api_key) {
            return new WP_Error('no_api_key', 'API key not configured.');
        }

        $cache_key = 'completion_' . md5($message . '_' . ($assistant_id ? $assistant_id : '0'));
        $cached = $this->cache->get($cache_key);
        if ($cached) {
            return $cached;
        }

        try {
            // Get enriched context with all available context types
            $enriched_context = $this->context_handler->get_enriched_context($message, [
                'assistant_id' => $assistant_id,
                'include_user_context' => true,
                'include_conversation_history' => true
            ]);

            $is_generation_request = $this->detect_generation_request($message);

            $messages = [
                $this->prompts_handler->get_system_instructions($assistant_id)
            ];

            if (!empty($enriched_context)) {
                $messages[] = [
                    'role' => 'system',
                    'content' => $enriched_context
                ];
            }

            // Add conversation context if provided
            if (!empty($conversation_context) && isset($conversation_context['history'])) {
                foreach ($conversation_context['history'] as $exchange) {
                    if (isset($exchange['user'])) {
                        $messages[] = [
                            'role' => 'user',
                            'content' => $exchange['user']
                        ];
                    }
                    if (isset($exchange['bot'])) {
                        $messages[] = [
                            'role' => 'assistant',
                            'content' => $exchange['bot']
                        ];
                    }
                }
            }

            // Add current user message
            $messages[] = [
                'role' => 'user',
                'content' => $message
            ];

            if (!empty($this->ml_patterns)) {
                $messages[] = [
                    'role' => 'system',
                    'content' => "Previous patterns:\n" . $this->format_ml_patterns($this->ml_patterns)
                ];
            }

            // Get response format settings
            $response_format = get_option('qkb_response_format', 'markdown');
            $response_format_param = [];

            if ($response_format === 'json') {
                $response_format_param = [
                    'response_format' => ['type' => 'json_object']
                ];
            }

            // Build request parameters
            $request_params = [
                'model' => $this->model,
                'messages' => $messages,
                'temperature' => $is_generation_request ? 0.7 : $this->temperature,
                'max_tokens' => $this->max_tokens,
                'presence_penalty' => $is_generation_request ? 0.3 : $this->presence_penalty,
                'frequency_penalty' => $is_generation_request ? 0.3 : $this->frequency_penalty,
            ];

            // Add response format if specified
            if (!empty($response_format_param)) {
                $request_params = array_merge($request_params, $response_format_param);
            }

            $response = $this->make_request('chat/completions', $request_params, 15); // Reduced timeout

            if (is_wp_error($response)) {
                if (strpos($response->get_error_message(), 'cURL error 28') !== false) {
                    // Retry once with increased timeout
                    $response = $this->make_request('chat/completions', [
                        'model' => $this->model,
                        'messages' => $messages,
                        'temperature' => $is_generation_request ? 0.7 : 0.3,
                        'max_tokens' => $this->max_tokens,
                        'presence_penalty' => $is_generation_request ? 0.3 : 0.1,
                        'frequency_penalty' => $is_generation_request ? 0.3 : 0.1,
                    ], 30);
                }
                if (is_wp_error($response)) {
                    throw new Exception($response->get_error_message());
                }
            }

            $body = json_decode(wp_remote_retrieve_body($response), true);
            if (!isset($body['choices'][0]['message']['content'])) {
                throw new Exception('Invalid API response format');
            }

            $content = trim($body['choices'][0]['message']['content']);
            $this->cache->set($cache_key, $content, 3600);

            return $content;

        } catch (Exception $e) {
            return $this->handle_error('Completion error', $e);
        }
    }

    private function get_completion_with_citations($messages) {
        try {
            $response = $this->make_request('chat/completions', [
                'model' => $this->model,
                'messages' => $messages,
                'temperature' => 0.3,
                'max_tokens' => $this->max_tokens
            ]);

            if (is_wp_error($response)) {
                throw new Exception($response->get_error_message());
            }

            $body = json_decode(wp_remote_retrieve_body($response), true);
            return $body['choices'][0]['message']['content'];

        } catch (Exception $e) {
            $this->error_handler->log_error('Citation completion error', $e);
            return $messages[count($messages) - 2]['content'];
        }
    }

    private function detect_generation_request($message) {
        $generation_keywords = [
            'create', 'generate', 'make', 'write', 'summarize',
            'explain', 'list', 'faqs', 'questions', 'summary'
        ];

        $message_lower = strtolower($message);
        foreach ($generation_keywords as $keyword) {
            if (strpos($message_lower, $keyword) !== false) {
                return true;
            }
        }
        return false;
    }

    private function format_ml_patterns($patterns) {
        return implode("\n", array_map(function($pattern) {
            return sprintf(
                "- Pattern: %s\n  Response: %s\n  Confidence: %.2f",
                $pattern['pattern'],
                $pattern['response'],
                $pattern['confidence']
            );
        }, $patterns));
    }
}
