/* Chatbot <PERSON> */

.qkb-chat-button {
  position: fixed;
  bottom: 0px;
  right: var(--qkb-gap-xl);
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 320px;
  padding: var(--qkb-padding-sm);
  border-radius: var(--qkb-radius-md) var(--qkb-radius-md) 0 0;
  background: var(--qkb-gradient);
  cursor: pointer;
  transition: var(--qkb-transition);
  gap: var(--qkb-gap);
  font-family: var(--qkb-font);
  font-weight: bold;
  z-index: calc(var(--qkb-z-index-modal) - 1);
}

/* Responsive adjustments for chat button */
@media screen and (max-width: 768px) {
  .qkb-chat-button {
    width: 90%;
    right: 5%;
    left: 5%;
  }
}

@media screen and (max-width: 480px) {
  .qkb-chat-button {
    width: 100%;
    right: 0;
    left: 0;
  }
}

.qkb-chat-button:hover {
  background: var(--qkb-primary-dark);
}

.qkb-chat-branding {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: var(--qkb-gap-md);
}

.qkb-chat-button-text {
  color: var(--qkb-bg-light);
  font-size: var(--qkb-font-size-md);
  letter-spacing: var(--qkb-letter-spacing);
}

.qkb-bot-avatar {
  width: 38px;
  height: 38px;
  max-width: 100%;
  max-width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px;
  background: var(--qkb-primary-light);
  border-radius: var(--qkb-radius-xl);
  -webkit-border-radius: var(--qkb-radius-xl);
  -moz-border-radius: var(--qkb-radius-xl);
  -ms-border-radius: var(--qkb-radius-xl);
  -o-border-radius: var(--qkb-radius-xl);
}

img.qkb-avatar.qkb-default-avatar {
	border-radius:50% !important;
}

.qkb-welcome-icon img {
	border-radius: 50% !important;
	width:100%;
}

.qkb-splash-logo img {
	border-radius:50% !important;
}

/* Apply filter only to SVG images */
.qkb-bot-avatar img.qkb-svg-image {
  filter: var(--qkb-filter);
}

/* End Chatbot Button */

.qkb-chatbot-container {
  display: none;
  position: fixed;
  flex-direction: column;
  bottom: 0;
  right: var(--qkb-gap-xl);
  width: 320px;
  height: 480px;
  max-width: calc(100vw - 40px);
  max-height: calc(100vh - 40px);
  background: var(--qkb-bg-light);
  border-radius: var(--qkb-radius-md) var(--qkb-radius-md) 0 0;
  box-shadow: var(--qkb-shadow);
  z-index: var(--qkb-z-index-modal);
  font-family: var(--qkb-font);
  transform: translateY(100%);
  opacity: 0;
}

.qkb-chatbot-container.visible {
  display: flex;
  opacity: 1;
  transform: translateY(0);
}

/* Responsive adjustments for chatbot container */
@media screen and (max-width: 768px) {
  .qkb-chatbot-container {
    width: 90%;
    right: 5%;
    left: 5%;
    height: 70vh;
  }
}

@media screen and (max-width: 480px) {
  .qkb-chatbot-container {
    width: 100%;
    right: 0;
    left: 0;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
    top: 0;
    bottom: 0;
    z-index: 999999;
  }
}

/* Mobile view specific styles */
.qkb-mobile-view {
  width: 100% !important;
  right: 0 !important;
  left: 0 !important;
  height: 100vh !important;
  max-height: 100vh !important;
  max-width: 100% !important;
  border-radius: 0 !important;
  top: 0 !important;
  bottom: 0 !important;
  position: fixed !important;
  z-index: 999999 !important;
}

/* Ensure smooth transitions for all elements */
.qkb-chatbot-header,
.qkb-chatbot-messages,
.qkb-input-container {
  transition: all 0.3s ease-in-out;
}

/* Mobile full screen styles */
.qkb-mobile-fullscreen-active {
  overflow: hidden !important;
  position: fixed;
  width: 100%;
  height: 100%;
}

/* Adjust header for mobile view */
.qkb-mobile-view .qkb-chatbot-header {
  padding: 10px;
  justify-content: space-between;
  border-radius: 0;
}

/* Adjust input container for mobile view */
.qkb-mobile-view .qkb-input-container {
  border-radius: 0;
  padding: 15px 50px 15px 15px;
}

/* Adjust messages container for mobile view */
.qkb-mobile-view .qkb-chatbot-messages {
  padding: 15px;
}

/* Status bar styling for mobile view */
.qkb-mobile-view:before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: env(safe-area-inset-top, 0);
  background: var(--qkb-primary-dark);
  z-index: 999999;
}

.qkb-backdrop {
  position: fixed;
  inset: 0;
  background: var(--qkb-primary-light);
  opacity: 0;
  transition: var(--qkb-transition);
  pointer-events: none;
  transform: translateZ(0);
}

.qkb-backdrop.active {
  opacity: 1;
  pointer-events: auto;
}

.qkb-chatbot-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--qkb-gap-md);
  background: var(--qkb-gradient);
  border-radius: var(--qkb-radius-md) var(--qkb-radius-md) 0 0;
}
.qkb-chatbot-branding {
  display: flex;
  align-items: center;
  gap: var(--qkb-gap);
}

.qkb-bot-info {
  display: flex;
  flex-direction: column;
}

.qkb-bot-name {
  font-size: var(--qkb-font-size-md);
  font-weight: bold;
  letter-spacing: 0.05em;
  color: var(--qkb-bg);
}

.qkb-assistant-name {
  font-size: var(--qkb-font-size-xs);
  color: var(--qkb-bg) !important;
  opacity: 0.7;
  display: none;
}

/* Chatbot Controls */

.qkb-chatbot-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--qkb-gap-md);
}

.qkb-chatbot-control-button {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0px;
  margin: 0px;
  color: var(--qkb-bg-light);
  line-height: 1;
  opacity: 0.8;
}

.qkb-chatbot-control-button:hover {
  background: transparent;
  color: var(--qkb-bg);
  opacity: 1;
  transform: var(--qkb-transform);
}

.qkb-chatbot-control-button:focus {
  background: transparent;
}

.qkb-new-chat-button {
  display: flex;
  align-items: center;
  font-size: var(--qkb-font-size-xs);
  font-weight: bold;
  color: var(--qkb-bg);
  opacity: 1;
}

/* End Chatbot Controls */

/* Chatbot Messaging */

.qkb-chatbot-messages {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  padding: var(--qkb-gap-md);
  gap: var(--qkb-gap);
  background: linear-gradient(
    135deg,
    var(--qkb-primary-light) 0%,
    var(--qkb-primary-light) 100%
  );
  height: calc(100% - 120px); /* Fallback height calculation */
}

.qkb-message {
  max-width: 85%;
  display: flex;
  flex-direction: column;
}

.qkb-user-message {
  align-self: flex-end;
}

.qkb-bot-message {
  align-self: flex-start;
}

.qkb-message-content {
  padding: var(--qkb-gap);
  border-radius: var(--qkb-radius-md);
  font-size: var(--qkb-font-size-sm);
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}

/* Responsive message adjustments */
@media screen and (max-width: 480px) {
  .qkb-message {
    max-width: 90%;
  }

  .qkb-message-content {
    font-size: calc(var(--qkb-font-size-sm) - 1px);
  }
}

.qkb-message-content p,
.qkb-message-content span,
.qkb-message-content a,
.qkb-message-content strong,
.qkb-message-content em,
.qkb-message-content b,
.qkb-message-content i,
.qkb-message-content u,
.qkb-message-content s,
.qkb-message-content code,
.qkb-message-content pre,
.qkb-message-content ol,
.qkb-message-content ul,
.qkb-message-content li,
.qkb-message-content blockquote,
.qkb-message-content h1,
.qkb-message-content h2,
.qkb-message-content h3,
.qkb-message-content h4,
.qkb-message-content h5,
.qkb-message-content h6 {
  font-size: var(--qkb-font-size-sm) !important;
}

.qkb-user-message .qkb-message-content {
  background: var(--qkb-primary);
  color: var(--qkb-bg);
  border-radius: var(--qkb-radius-sm) var(--qkb-radius-sm) 0
    var(--qkb-radius-sm);
  padding: var(--qkb-padding-sm);
}

.qkb-bot-message .qkb-message-content {
  background: var(--qkb-bg);
  color: var(--qkb-text);
  border-radius: var(--qkb-radius-md) var(--qkb-radius-sm) var(--qkb-radius-sm)
    0;
  padding: var(--qkb-padding-sm);
}

.qkb-message-time {
  font-size: var(--qkb-font-size-xs);
  color: var(--qkb-text-light);
  opacity: 0.5;
}

.qkb-chatbot-messages.has-chat-history {
  opacity: 1;
  visibility: visible;
}

.qkb-error-message {
  color: var(--qkb-error);
  font-size: var(--qkb-font-size-xs);
  padding: var(--qkb-gap);
  text-align: center;
  display: block;
}

.qkb-error-message .qkb-message-content,
.qkb-error-message .qkb-error-text {
  background: #fee2e2;
  color: var(--qkb-error);
  display: flex;
  align-items: center;
  gap: 8px;
}

/* End Chatbot Messaging */

/* Chatbot Input */

.qkb-input-container {
  position: relative;
  display: flex;
  align-items: flex-end;
  background: var(--qkb-bg);
  border: none;
  border-radius: 0 0 var(--qkb-radius-md) var(--qkb-radius-md);
  transition: var(--qkb-transition);
  font-size: var(--qkb-font-size-sm);
  padding: 15px 45px 15px 10px;
  min-height: 60px;
}

@media screen and (max-width: 480px) {
  .qkb-input-container {
    padding: 10px 45px 10px 8px;
  }
}

.qkb-input-textarea {
  flex: 1;
  border: none;
  background: transparent;
  font-size: var(--qkb-font-size-sm);
  resize: none;
  color: var(--qkb-text);
}

.qkb-input-textarea:focus {
  outline: none;
}

.qkb-send-button {
  position: absolute;
  right: 10px;
  bottom: 10px;
  width: 40px;
  height: 40px;
  border-radius: var(--qkb-radius-lg) var(--qkb-radius-lg) 0
    var(--qkb-radius-lg);
  background: var(--qkb-gradient);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--qkb-transition);
  padding: 0px;
}

.qkb-send-button:disabled {
  cursor: not-allowed;
  background: var(--qkb-primary-light);
}

.qkb-send-button:not(:disabled):hover {
  transform: var(--qkb-transform);
}

.qkb-send-button svg {
  stroke: var(--qkb-bg);
  fill: none;
  transition: var(--qkb-transition);
}

.qkb-send-button:hover:not(:disabled) svg {
  transform: rotate(35deg);
}

/* End Chatbot Input */

/* Chatbot Scroll */

.qkb-chatbot-messages::-webkit-scrollbar {
  width: 6px;
}

.qkb-chatbot-messages::-webkit-scrollbar-track {
  background: transparent;
}

.qkb-chatbot-messages::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

/* End Chatbot Scroll */

/* Typing Indicator */

.qkb-typing-content {
  display: flex;
  align-items: center;
  gap: var(--qkb-gap);
}

.qkb-typing-q-icon {
  width: 20px;
  height: 20px;
  color: var(--qkb-primary);
}

.qkb-typing-text {
  color: var(--qkb-text);
  font-size: var(--qkb-font-size-xs);
}

.qkb-typing-dots {
  display: flex;
  gap: 4px;
}

.qkb-typing-dot {
  width: 6px;
  height: 6px;
  background: var(--qkb-primary);
  border-radius: 50%;
  opacity: 0.4;
}

.qkb-typing-dot:nth-child(1) {
  animation: qkbDotBounce 1.4s infinite;
}

.qkb-typing-dot:nth-child(2) {
  animation: qkbDotBounce 1.4s infinite 0.2s;
}

.qkb-typing-dot:nth-child(3) {
  animation: qkbDotBounce 1.4s infinite 0.4s;
}

@keyframes qkbDotBounce {
  0%,
  60%,
  100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-4px);
    opacity: 1;
  }
}

/* End Typing Indicator */

/* Chatbot Feedback */

.qkb-message-feedback {
  margin-top: var(--qkb-gap);
  display: flex;
  align-items: center;
  opacity: 0;
  transition: var(--qkb-transition);
  gap: var(--qkb-gap);
}

.qkb-message-feedback.visible {
  opacity: 1;
}

.qkb-feedback-prompt {
  color: var(--qkb-text-light);
  font-size: var(--qkb-font-size-xs);
}

.qkb-feedback-buttons {
  display: flex;
  gap: var(--qkb-gap);
}

.qkb-feedback-btn {
  background: transparent;
  border: none;
  padding: var(--qkb-gap);
  cursor: pointer;
  color: var(--qkb-text-light);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--qkb-transition);
}

.qkb-feedback-btn:hover {
  color: var(--qkb-text);
  background: transparent;
}

.qkb-feedback-btn[data-value="helpful"]:hover {
  color: var(--qkb-success);
}

.qkb-feedback-btn[data-value="not_helpful"]:hover {
  color: var(--qkb-error);
}

.feedback-submitted .qkb-feedback-buttons {
  display: none;
}

/* End Chatbot Feedback */

/* Chatbot Welcome Screen */

.qkb-welcome-screen {
  position: relative;
  padding: var(--qkb-gap-md);
  opacity: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  animation: qkbWelcomeIn 0.8s ease-out forwards;
}

.qkb-welcome-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  opacity: 0.5;
  pointer-events: none;
  background-color: transparent;
  display: block;
}

.qkb-welcome-content {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.qkb-welcome-header {
  margin-bottom: var(--qkb-gap-xl);
  animation: qkbFadeInUp 0.5s ease-out forwards;
  animation-delay: 0.2s;
  opacity: 0;
}

.qkb-welcome-heading {
  font-size: var(--qkb-font-size-md);
  font-weight: bold;
  color: var(--qkb-primary-dark);
  margin-bottom: var(--qkb-gap-sm);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.qkb-welcome-subheading {
  font-size: var(--qkb-font-size-sm);
  color: var(--qkb-text);
  opacity: 0.8;
}

.qkb-welcome-suggestions {
  margin-bottom: var(--qkb-gap-xl);
  animation: qkbFadeInUp 0.5s ease-out forwards;
  animation-delay: 0.4s;
  opacity: 0;
}

.qkb-suggestions-title {
  font-size: var(--qkb-font-size-sm);
  color: var(--qkb-text-light);
  margin-bottom: var(--qkb-gap-md);
  font-weight: 500;
}

.qkb-suggestion-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--qkb-gap-md);
}

.qkb-suggestion-button {
  background: var(--qkb-bg);
  border: 1px solid var(--qkb-border);
  border-radius: var(--qkb-radius-xs);
  padding: var(--qkb-padding-sm);
  font-size: var(--qkb-font-size-xs);
  color: var(--qkb-text);
  cursor: pointer;
  transition: var(--qkb-transition);
  -webkit-border-radius: var(--qkb-radius-xs);
  -moz-border-radius: var(--qkb-radius-xs);
  -ms-border-radius: var(--qkb-radius-xs);
  -o-border-radius: var(--qkb-radius-xs);
}

.qkb-suggestion-button:hover {
  background: var(--qkb-primary-light);
  border-color: var(--qkb-primary);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(34, 113, 177, 0.2);
}

@keyframes qkbWelcomeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes qkbPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(34, 113, 177, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(34, 113, 177, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(34, 113, 177, 0);
  }
}

/* End Chatbot Welcome Screen */

/* Chatbot Modal */

.qkb-new-chat-modal {
  display: none;
  position: absolute;
  inset: 0;
  z-index: var(--qkb-z-index-modal);
}

.qkb-new-chat-modal.active {
  display: flex;
  align-items: center;
  justify-content: center;
}

.qkb-modal-overlay {
  position: absolute;
  inset: 0;
  background-color: var(--qkb-primary-light);
  backdrop-filter: var(--qkb-blur);
  -webkit-backdrop-filter: var(--qkb-blur);
  border-radius: var(--qkb-radius-md) var(--qkb-radius-md) 0 0;
}

.qkb-modal-container {
  position: relative;
  z-index: 1;
  background: var(--qkb-bg);
  border-radius: var(--qkb-radius-sm);
  padding:var(--qkb-gap-xl);
  width: 80%;
  max-width: 400px;
  box-shadow: var(--qkb-shadow);
  -webkit-border-radius: var(--qkb-radius-sm);
  -moz-border-radius: var(--qkb-radius-sm);
  -ms-border-radius: var(--qkb-radius-sm);
  -o-border-radius: var(--qkb-radius-sm);
}

.qkb-modal-content-chat {
  margin-bottom: var(--qkb-gap-lg);
  text-align: center;
}

.qkb-modal-content-chat h2 {
  color: var(--qkb-text);
  font-size: var(--qkb-font-size-md);
  font-weight: var(--qkb-font-weight-bold);
  margin-bottom: var(--qkb-gap-lg);
}

.qkb-modal-content-chat p {
  color: var(--qkb-text-light);
  font-size: var(--qkb-font-size-sm);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.qkb-modal-actions-chat {
  display: flex;
  gap: var(--qkb-gap-md);
  justify-content: center;
}

.qkb-modal-actions-chat button {
  padding: var(--qkb-padding-sm);
  font-size: var(--qkb-font-size-xs);
  border: 1px solid transparent;
  border-radius: var(--qkb-radius-xs);
  -webkit-border-radius: var(--qkb-radius-xs);
  -moz-border-radius: var(--qkb-radius-xs);
  -ms-border-radius: var(--qkb-radius-xs);
  -o-border-radius: var(--qkb-radius-xs);
}

.qkb-confirm-new-chat {
  background: var(--qkb-primary);
  color: var(--qkb-bg);
}

.qkb-confirm-new-chat:hover {
  background: var(--qkb-primary-dark);
}

.qkb-cancel-new-chat {
  background: var(--qkb-bg-light);
  color: var(--qkb-text-light);
}

.qkb-cancel-new-chat:hover {
  border: 1px solid var(--qkb-border);
  background: var(--qkb-border);
  color: var(--qkb-text-light);
}

/* Quick Access Modal */
.qkb-quick-access-modal {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--qkb-z-index-modal);
  font-family: var(--qkb-font);
}

.qkb-quick-access-modal.active {
  display: block;
}

.qkb-quick-access-modal .qkb-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--qkb-primary-light);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.qkb-quick-access-modal .qkb-modal-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--qkb-bg);
  border-radius: var(--qkb-radius-sm);
  width: 90%;
  max-width: 90%;
  height: 80%;
  max-height: 80%;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  animation: modalFadeIn 0.2s ease-out;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -48%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}

.qkb-quick-access-modal .qkb-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--qkb-gap-md);
  background: transparent;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.qkb-quick-access-modal .qkb-modal-title {
  font-size: var(--qkb-font-size-md);
  font-weight: 600;
  margin: 0;
  color: var(--qkb-text-color);
}

.qkb-quick-access-modal .qkb-modal-close {
  background: transparent;
  border: none;
  color: var(--qkb-text-color);
  font-size: 20px;
  cursor: pointer;
  padding: 5px;
  line-height: 1;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.qkb-quick-access-modal .qkb-modal-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.qkb-quick-access-modal .qkb-modal-content {
  padding: var(--qkb-gap-md);
  overflow-y: auto;
  flex: 1;
  background-color: var(--qkb-bg);
}

/* Style content inside modal */
.qkb-quick-access-modal .qkb-modal-content p {
  margin-bottom: 1em;
  line-height: 1.6;
}

.qkb-quick-access-modal .qkb-modal-content h1,
.qkb-quick-access-modal .qkb-modal-content h2,
.qkb-quick-access-modal .qkb-modal-content h3,
.qkb-quick-access-modal .qkb-modal-content h4 {
  margin-top: 1.5em;
  margin-bottom: 0.75em;
  font-weight: 600;
}

.qkb-quick-access-modal .qkb-modal-content h1:first-child,
.qkb-quick-access-modal .qkb-modal-content h2:first-child,
.qkb-quick-access-modal .qkb-modal-content h3:first-child,
.qkb-quick-access-modal .qkb-modal-content h4:first-child {
  margin-top: 0;
}

.qkb-quick-access-modal .qkb-modal-content ul,
.qkb-quick-access-modal .qkb-modal-content ol {
  margin-left: 1.5em;
  margin-bottom: 1em;
}

.qkb-quick-access-modal .qkb-modal-content li {
  margin-bottom: 0.5em;
}

.qkb-quick-access-modal .qkb-modal-content a {
  color: var(--qkb-primary);
  text-decoration: none;
}

.qkb-quick-access-modal .qkb-modal-content a:hover {
  text-decoration: underline;
}

/* Responsive adjustments */
@media screen and (max-width: 480px) {
  .qkb-quick-access-modal .qkb-modal-container {
    width: 95%;
    height: 85%;
  }

  .qkb-quick-access-modal .qkb-modal-content {
    padding: var(--qkb-gap-sm);
  }
}

/* End Chatbot Modal */

/* Chatbot Splash Screen */

.qkb-splash-screen {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--qkb-primary-dark);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: calc(var(--qkb-z-index-modal) + 1);
  border-radius: var(--qkb-radius-md) var(--qkb-radius-md) 0 0;
  overflow: hidden;
  opacity: 1;
  visibility: visible;
}

.qkb-splash-circles {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.qkb-splash-circle {
  position: absolute;
  background: var(--qkb-primary);
  border-radius: 50%;
  animation: qkbSplashCircle 4s infinite;
}

.qkb-splash-circle:nth-child(1) {
  width: 200px;
  height: 200px;
  top: -100px;
  right: -100px;
  animation-delay: 0.1s;
}

.qkb-splash-circle:nth-child(2) {
  width: 150px;
  height: 150px;
  bottom: -75px;
  left: -75px;
  animation-delay: 0.5s;
}

.qkb-splash-circle:nth-child(3) {
  width: 100px;
  height: 100px;
  top: 50%;
  right: -50px;
  animation-delay: 1s;
}

.qkb-splash-content {
  text-align: center;
  transform: translateY(20px);
  opacity: 0;
  animation: qkbSplashContentIn 0.8s ease-out forwards;
}

.qkb-splash-logo {
  width: 80px;
  height: 80px;
  margin: 0 auto 40px;
  animation: qkbLogoSpin 2s infinite;
  position: relative;
}

.qkb-splash-logo img.qkb-svg-image {
  filter: var(--qkb-filter);
}

.qkb-splash-logo::after {
  content: "";
  position: absolute;
  inset: -10px;
  border: 2px solid var(--qkb-primary-light);
  border-radius: 50%;
  animation: qkbLogoPulse 2s infinite;
}

.qkb-splash-title {
  font-size: calc(var(--qkb-font-size-sm) + var(--qkb-font-size-sm));
  font-weight: bold;
  margin: 0 0 10px;
  background: linear-gradient(135deg, #fff, rgba(255, 255, 255, 0.8));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  animation: qkbTitleGlow 2s infinite;
}

.qkb-splash-subtitle {
  font-size: var(--qkb-font-size-sm);
  color: var(--qkb-bg-light);
  opacity: 0;
  animation: qkbFadeInUp 0.5s ease-out forwards;
  animation-delay: 0.5s;
}

.qkb-splash-loader {
  margin-top: 30px;
  width: 200px;
  height: 4px;
  background: var(--qkb-primary-light);
  border-radius: 2px;
  overflow: hidden;
}

.qkb-splash-loader-bar {
  width: 0%;
  height: 100%;
  background: var(--qkb-bg);
  animation: qkbLoaderProgress 3.5s ease-out forwards;
}

.qkb-splash-powered {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  text-align: center;
  font-size: var(--qkb-font-size-sm);
  color: var(--qkb-bg);
  opacity: 0;
  animation: qkbFadeInUp 0.5s ease-out forwards;
  animation-delay: 1s;
}

@keyframes qkbSplashCircle {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 0.1;
  }
  50% {
    transform: scale(1.5) rotate(180deg);
    opacity: 0.2;
  }
  100% {
    transform: scale(1) rotate(360deg);
    opacity: 0.1;
  }
}

@keyframes qkbLogoSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes qkbLogoPulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

@keyframes qkbTitleGlow {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes qkbLoaderProgress {
  0% {
    width: 0%;
  }
  20% {
    width: 20%;
  }
  50% {
    width: 60%;
  }
  80% {
    width: 85%;
  }
  100% {
    width: 100%;
  }
}

@keyframes qkbFadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes qkbSplashFadeOut {
  from {
    opacity: 1;
    transform: scale(1);
    visibility: visible;
  }
  to {
    opacity: 0;
    transform: scale(0.95);
    visibility: hidden;
  }
}

@keyframes qkbSplashContentIn {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  20% {
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.qkb-splash-screen.fade-out {
  animation: qkbSplashFadeOut 0.5s ease-out forwards;
}

/* End Chatbot Splash Screen */

/* Toast Notifications */
.qkb-toast {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--qkb-bg);
  color: var(--qkb-text);
  padding: 12px 20px;
  border-radius: var(--qkb-radius-sm);
  box-shadow: var(--qkb-shadow-sm);
  display: flex;
  align-items: center;
  gap: 10px;
  z-index: var(--qkb-z-index-modal);
  animation: qkbSlideIn 0.3s ease-out;
}

@keyframes qkbSlideIn {
  from {
    transform: translate(-50%, 100%);
    opacity: 0;
  }
  to {
    transform: translate(-50%, 0);
    opacity: 1;
  }
}

.qkb-toast-success {
  background: var(--qkb-success);
  color: white;
}

.qkb-toast-error {
  background: var(--qkb-error);
  color: white;
}

/* End Toast Notifications */

/* Reset absolute positioning for inline character count */
.qi-character-count-inline {
  position: static !important;
  bottom: auto !important;
  right: auto !important;
  margin-right: 10px !important;
  opacity: 1 !important;
}

/* Adjust the submit buttons container to properly align items */

/* Ensure proper spacing in mobile view */
@media screen and (max-width: 480px) {
  .qi-character-count-inline {
    margin-right: 8px !important;
  }
}

.qkb-chatbot-container .qi-input-container {
  background: var(--qkb-bg);
  border-radius: 0;
  border: none;
  overflow: hidden;
  width: 100%;
  margin: 0;
  min-height: 100px;
  max-height: 200px;
  contain: layout;
  position: relative;
}

.qkb-chatbot-container .qi-input-area {
  overflow-y: auto;
  overflow-x: hidden;
  height: 30px;
  max-height: 60px;
}

.qkb-chatbot-container .qi-input-area textarea {
  width: 100%;
  border: none;
  background: transparent;
  font-size: var(--qkb-font-size-sm);
  color: var(--qkb-text);
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
  resize: none;
  padding: 0;
  font-family: inherit;
  height: auto;
  min-height: 24px;
  overflow-x: hidden;
}

.qkb-chatbot-container .qi-input-area textarea:focus {
  outline: none;
}

.qkb-chatbot-container .qi-input-area textarea::placeholder {
  color: var(--qkb-text-light);
  opacity: 0.8;
}

/* Character counter */
.qkb-chatbot-container .qi-character-count {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: var(--qkb-font-size-xs);
  color: var(--qkb-text-light) !important;
  opacity: 0.7 !important;
  transition: var(--qkb-transition);
  display: flex;
  align-items: center;
  gap: 6px;
  z-index: 5;
  -webkit-transition: var(--qkb-transition);
  -moz-transition: var(--qkb-transition);
  -ms-transition: var(--qkb-transition);
  -o-transition: var(--qkb-transition);
}

.qkb-chatbot-container .qi-input-container:focus-within .qi-character-count {
  opacity: 1;
}

/* Keyboard shortcut */

.qkb-chatbot-container .qi-shortcut-key {
  display: inline-block;
  padding: 2px 5px;
  background: var(--qkb-bg-light);
  border-radius: 3px;
  margin-right: 4px;
  font-weight: 500;
}

/* Input buttons */
.qkb-chatbot-container .qi-input-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 5px;
}

.qkb-chatbot-container .qi-utility-buttons {
  display: flex;
  gap: var(--qkb-gap);
}

.qkb-chatbot-container .qi-utility-buttons button {
  background: transparent;
  border: 1px solid var(--qkb-border-light);
  border-radius: var(--qkb-radius-xs);
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--qkb-text-light);
  -webkit-border-radius: var(--qkb-radius-xs);
  -moz-border-radius: var(--qkb-radius-xs);
  -ms-border-radius: var(--qkb-radius-xs);
  -o-border-radius: var(--qkb-radius-xs);
}

.qkb-chatbot-container .qi-utility-buttons button:hover {
  background: transparent;
  color: var(--qkb-primary);
}

.qkb-chatbot-container .qi-hidden-file-input {
  display: none;
}

/* Send button */
.qkb-chatbot-container .qi-send-button {
  padding: var(--qkb-padding-sm);
  border-radius: var(--qkb-radius-xs);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--qkb-transition);
  -webkit-border-radius: var(--qkb-radius-xs);
  -moz-border-radius: var(--qkb-radius-xs);
  -ms-border-radius: var(--qkb-radius-xs);
  -o-border-radius: var(--qkb-radius-xs);
}



.qkb-chatbot-container .qi-send-button svg {
  stroke: var(--qkb-bg);
  fill: none;
  transition: var(--qkb-transition);
}


/* Quick Access Utility Button */
.qkb-chatbot-container .qi-quick-access-utility-button,
.qkb-chatbot-container .qkb-quick-access-utility-button {
  position: relative;
}

.qkb-chatbot-container .qi-quick-access-utility-button:hover,
.qkb-chatbot-container .qkb-quick-access-utility-button:hover {
  color: var(--qkb-primary);
}

/* Active state for quick access utility button */
.qkb-chatbot-container .qi-quick-access-utility-button.active,
.qkb-chatbot-container .qkb-quick-access-utility-button.active {
  background-color: var(--qkb-border);
  color: var(--qkb-primary);
  border-color: var(--qkb-primary);
}

/* Mobile adjustments */
@media screen and (max-width: 480px) {
  .qkb-chatbot-container .qi-input-container {
    padding: 8px 12px;
    min-height: 100px;
  }
}
