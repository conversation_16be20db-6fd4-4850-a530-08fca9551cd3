<?php

class QKB_Cache {
    private $cache = [];

    public function get($key) {
        if (isset($this->cache[$key])) {
            return $this->cache[$key];
        }
        return false;
    }

    public function set($key, $value, $expiration = 3600) {
        $this->cache[$key] = $value;
        // Optionally, you can implement expiration logic here
    }

    public function delete($key) {
        if (isset($this->cache[$key])) {
            unset($this->cache[$key]);
        }
    }

    public function set_ml_pattern($key, $value, $metadata = []) {
        $cache_data = [
            'value' => $value,
            'metadata' => array_merge($metadata, [
                'timestamp' => time(),
                'usage_count' => 0,
                'success_rate' => 0
            ])
        ];
        
        $this->cache[$key] = $cache_data;
        $this->update_pattern_statistics($key);
    }
} 