<?php
if (!defined('ABSPATH')) {
    exit;
}


class QKB_Knowledge_Base
{

    private static $instance = null;

    private function __construct()
    {

        add_action('init', [$this, 'register_post_type']);

        add_action('init', [$this, 'register_taxonomy'], 0);

        add_action('elementor/widgets/register', [$this, 'register_widgets']);

        add_action('init', [$this, 'init'], 0);

        // Add hooks for assistant taxonomy fields
        add_action('kb_assistant_add_form_fields', [$this, 'add_assistant_prompt_field']);
        add_action('kb_assistant_edit_form_fields', [$this, 'edit_assistant_prompt_field']);
        add_action('created_kb_assistant', [$this, 'save_assistant_prompt']);
        add_action('edited_kb_assistant', [$this, 'save_assistant_prompt']);

        // Add AJAX handlers for assistants
        add_action('wp_ajax_qkb_generate_prompt', [$this, 'ajax_generate_prompt']);

        // Remove the Assistants taxonomy from admin menu
        add_action('admin_menu', [$this, 'remove_assistants_menu'], 999);

        // Remove the "Add New Knowledge Base" menu item
        add_action('admin_menu', [$this, 'remove_add_new_menu'], 999);

        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);

        add_action('add_meta_boxes', array($this, 'add_file_upload_meta_box'));
        add_action('add_meta_boxes', array($this, 'add_embedding_meta_box'));
        add_action('save_post', array($this, 'save_file_upload_meta'));

        // Hook into post save to generate embeddings
        add_action('save_post_kb_knowledge_base', array($this, 'generate_post_embedding'), 10, 3);

        // Add columns to Knowledge Base admin list table
        add_filter('manage_kb_knowledge_base_posts_columns', array($this, 'add_embedding_column'));
        add_action('manage_kb_knowledge_base_posts_custom_column', array($this, 'render_embedding_column'), 10, 2);
        add_filter('manage_edit-kb_knowledge_base_sortable_columns', array($this, 'make_embedding_column_sortable'));
        add_action('pre_get_posts', array($this, 'embedding_column_orderby'));

        // Add debug logging
        add_action('admin_init', function () {
            error_log('QKB: Post types - ' . print_r(get_post_types(), true));
        });

        // Verify knowledge base setup
        $verification = $this->verify_knowledge_base();
        error_log('Knowledge base verification: ' . print_r($verification, true));

        if ($verification['status'] !== 'ok') {
            error_log('Knowledge base issues found: ' . implode(', ', $verification['issues']));
        }

        // Add AJAX handler for knowledge base submissions
        add_action('wp_ajax_qkb_add_knowledge', [$this, 'handle_knowledge_submission']);

        // Add AJAX handler for deleting a knowledge entry
        add_action('wp_ajax_qkb_delete_knowledge', [$this, 'ajax_delete_knowledge']);
        // Add AJAX handler for listing knowledge entries
        add_action('wp_ajax_qkb_list_knowledge', [$this, 'ajax_list_knowledge']);

        // Add AJAX handler for manually generating embeddings
        add_action('wp_ajax_qkb_generate_embedding', [$this, 'ajax_generate_embedding']);
        add_action('wp_ajax_qkb_generate_all_embeddings', [$this, 'ajax_generate_all_embeddings']);
    }

    /**
     * Get the singleton instance of this class
     *
     * @return QKB_Knowledge_Base
     */
    public static function get_instance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function register_post_type()
    {
        $labels = [
            'name' => _x('Knowledge Base', 'Post type general name', 'q-knowledge-base'),
            'singular_name' => _x('Knowledge Base', 'Post type singular name', 'q-knowledge-base'),
            'menu_name' => _x('Knowledge Base', 'Admin Menu text', 'q-knowledge-base'),
            'add_new' => __('Add New', 'q-knowledge-base'),
            'add_new_item' => __('Add New Knowledge Base', 'q-knowledge-base'),
            'edit_item' => __('Edit Knowledge Base', 'q-knowledge-base'),
            'new_item' => __('New Knowledge Base', 'q-knowledge-base'),
            'view_item' => __('View Knowledge Base', 'q-knowledge-base'),
            'search_items' => __('Search Knowledge Base', 'q-knowledge-base'),
            'not_found' => __('No knowledge base found', 'q-knowledge-base'),
            'not_found_in_trash' => __('No knowledge base found in Trash', 'q-knowledge-base'),
        ];

        $args = [
            'labels' => $labels,
            'public' => true,
            'publicly_queryable' => true,
            'show_ui' => true,
            'show_in_menu' => true,
            'query_var' => true,
            'rewrite' => ['slug' => 'knowledge-base'],
            'capability_type' => 'post',
            'has_archive' => false,
            'hierarchical' => false,
            'menu_position' => 5,
            'supports' => [
                'title',
                'editor',
                'elementor'
            ],
            'menu_icon' => 'data:image/svg+xml;base64,' . base64_encode('<?xml version="1.0" encoding="utf-8"?><svg width="800px" height="800px" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="iconify iconify--noto" preserveAspectRatio="xMidYMid meet"><path d="M102.61 107.83c10.8-9.85 16.84-24.08 16.84-39.9c0-29.73-21.33-53.82-55.45-53.82S8.55 38.21 8.55 67.93c0 29.73 21.33 53.82 55.45 53.82c7.69 0 14.72-1.23 21.01-3.47l5.97 8.54a2.079 2.079 0 0 0 2.28.81l16.51-4.75c.64-.19 1.16-.67 1.39-1.31c.22-.63.13-1.33-.26-1.89l-8.29-11.85zM64 95.89c-17.72 0-28.81-12.52-28.81-27.96S46.27 39.97 64 39.97s28.81 12.52 28.81 27.96c0 6.65-2.07 12.76-5.82 17.56l-7.26-10.38a2.086 2.086 0 0 0-2.28-.81l-16.52 4.75c-.64.19-1.16.67-1.39 1.31c-.22.63-.13 1.33.26 1.89l9.27 13.25c-1.62.24-3.3.39-5.07.39z" fill="#40C0E7"></path></svg>'),
            'show_in_rest' => true,
            'show_in_nav_menus' => true,
            'show_in_admin_bar' => true,
            'template_lock' => false,
        ];

        register_post_type('kb_knowledge_base', $args);

        if (did_action('elementor/loaded')) {
            \Elementor\Plugin::$instance->documents->register_document_type(
                'kb_knowledge_base',
                \Elementor\Core\DocumentTypes\Post::get_class_full_name()
            );
        }

        register_post_type('kb_external_content', array(
            'labels' => array(
                'name' => __('External Content', 'q-knowledge-base'),
                'singular_name' => __('External Content', 'q-knowledge-base'),
            ),
            'public' => false,
            'show_ui' => true,
            'show_in_menu' => 'edit.php?post_type=kb_knowledge_base',
            'supports' => array('title', 'editor'),
            'map_meta_cap' => true,
        ));
    }

    public function register_widgets($widgets_manager)
    {

        require_once QKB_PLUGIN_DIR . 'includes/elementor/class-qkb-knowledge-base-widget.php';

        $widgets_manager->register(new QKB_Knowledge_Base_Widget());
    }

    public function init()
    {
        add_post_type_support('kb_knowledge_base', 'elementor');

        add_filter('elementor/utils/get_public_post_types', function ($post_types) {
            $post_types['kb_knowledge_base'] = get_post_type_object('kb_knowledge_base');
            return $post_types;
        });
    }

    public function register_taxonomy()
    {
        $labels = array(
            'name' => _x('Assistants', 'taxonomy general name', 'q-knowledge-base'),
            'singular_name' => _x('assistant', 'taxonomy singular name', 'q-knowledge-base'),
            'search_items' => __('Search Assistants', 'q-knowledge-base'),
            'all_items' => __('All Assistants', 'q-knowledge-base'),
            'parent_item' => __('Parent assistant', 'q-knowledge-base'),
            'parent_item_colon' => __('Parent assistant:', 'q-knowledge-base'),
            'edit_item' => __('Edit assistant', 'q-knowledge-base'),
            'update_item' => __('Update assistant', 'q-knowledge-base'),
            'add_new_item' => __('Add New assistant', 'q-knowledge-base'),
            'new_item_name' => __('New assistant Name', 'q-knowledge-base'),
            'menu_name' => __('Assistants', 'q-knowledge-base'),
        );

        $args = array(
            'hierarchical' => true,
            'labels' => $labels,
            'show_ui' => true, // Keep UI enabled for programmatic access
            'show_in_menu' => false, // Hide from admin menu
            'show_admin_column' => true,
            'query_var' => true,
            'rewrite' => array('slug' => 'kb-assistant'),
            'show_in_rest' => true,
            'rest_base' => 'kb-assistants',
            'show_in_nav_menus' => true,
        );

        register_taxonomy('kb_assistant', array('kb_knowledge_base', 'kb_external_content'), $args);

        // Add default prompt for the default assistant
        if (!term_exists('Knowledge Base', 'kb_assistant')) {
            $term = wp_insert_term('Knowledge Base', 'kb_assistant', array(
                'description' => 'Primary Knowledge Base Assistant',
                'slug' => 'knowledge-base'
            ));

            if (!is_wp_error($term)) {
                $default_prompt = "# Primary Knowledge Base Assistant

## Core Identity
- Role: Central Knowledge Repository Manager
- Focus: Comprehensive Information Access
- Approach: User-Centric Knowledge Delivery

## Primary Responsibilities
1. Information Management
   - Maintain accurate knowledge delivery
   - Ensure content accessibility
   - Provide structured responses
   - Guide users effectively

2. Communication Standards
   - Professional yet approachable tone
   - Clear, concise explanations
   - Source-based responses
   - Markdown formatting

3. Quality Assurance
   - Verify information accuracy
   - Cross-reference sources
   - Maintain content integrity
   - Update outdated information

4. User Support
   - Guide through complex topics
   - Suggest relevant resources
   - Provide clear explanations
   - Acknowledge limitations

## Response Protocol
1. Answer Structure
   - Lead with key information
   - Include source citations
   - Use markdown formatting
   - Maintain clarity

2. Content Delivery
   - Accurate information only
   - Clear explanations
   - Relevant examples
   - Proper formatting

3. User Interaction
   - Professional tone
   - Helpful guidance
   - Clear boundaries
   - Constructive feedback

## Technical Guidelines
- Format: Markdown
- Citations: Required
- Sources: Knowledge base only
- Style: Professional";

                update_term_meta($term['term_id'], 'assistant_prompt', $default_prompt);
                update_term_meta($term['term_id'], 'assistant_icon', 'fas fa-robot');
                update_term_meta($term['term_id'], 'assistant_expertise', 'Knowledge Management,Information Retrieval,User Support');
                update_term_meta($term['term_id'], 'assistant_categories', array('General', 'Technical', 'Support'));
            }
        }
    }

    public function get_assistant_prompt($assistant_id)
    {
        $prompt = get_term_meta($assistant_id, 'assistant_prompt', true);

        if (empty($prompt)) {
            // Get default assistant
            $default_assistant = get_term_by('slug', 'knowledge-base', 'kb_assistant');
            if ($default_assistant) {
                // Try to get the default assistant's prompt
                $prompt = get_term_meta($default_assistant->term_id, 'assistant_prompt', true);

                // If default assistant prompt is empty, generate one
                if (empty($prompt)) {
                    $prompt = $this->get_default_prompt($default_assistant);
                }
            } else {
                // If no default assistant exists, create a basic prompt
                $prompt = "You are a knowledgeable assistant focused on providing accurate information from the knowledge base. " .
                    "Your responses should be clear, concise, and directly based on the available documentation. " .
                    "If you're unsure about something, acknowledge that and suggest where the user might find more information.";
            }
        } else {
            // Get the current assistant term
            $assistant = get_term($assistant_id, 'kb_assistant');
            if (!is_wp_error($assistant)) {
                // Check if prompt needs to be regenerated based on metadata
                $stored_expertise = get_term_meta($assistant_id, 'assistant_expertise', true);
                $stored_categories = get_term_meta($assistant_id, 'assistant_categories', true);

                // If metadata exists but isn't reflected in the prompt, regenerate it
                if (
                    ($stored_expertise || $stored_categories) &&
                    (!strpos($prompt, $stored_expertise) || !strpos($prompt, implode(', ', (array) $stored_categories)))
                ) {
                    $prompt = $this->get_default_prompt($assistant);
                }
            }
        }

        return $prompt;
    }


    public function add_assistant_prompt_field()
    {
        ?>
        <div class="qkb-assistant-form">
            <p class="description">
                <?php _e('Please use the Assistant Builder to configure this assistant.', 'q-knowledge-base'); ?>
            </p>
            <!-- Hidden field to store the actual prompt content -->
            <input type="hidden" name="assistant_prompt" id="assistant_prompt" value="">
        </div>
        <?php
    }

    public function edit_assistant_prompt_field($term)
    {
        $prompt = get_term_meta($term->term_id, 'assistant_prompt', true);
        ?>
        <tr class="form-field">
            <th scope="row">
                <label><?php _e('Assistant Configuration', 'q-knowledge-base'); ?></label>
            </th>
            <td>
                <p class="description">
                    <?php _e('Please use the Assistant Builder to configure this assistant.', 'q-knowledge-base'); ?>
                    <a href="<?php echo admin_url('edit.php?post_type=kb_knowledge_base&page=qkb-assistant-builder'); ?>" class="button">
                        <?php _e('Open Assistant Builder', 'q-knowledge-base'); ?>
                    </a>
                </p>
                <!-- Hidden field to store the actual prompt content -->
                <input type="hidden" name="assistant_prompt" id="assistant_prompt" value="<?php echo esc_attr($prompt); ?>">
            </td>
        </tr>
        <?php
    }

    public function save_assistant_prompt($term_id)
    {
        // Save the prompt content
        if (isset($_POST['assistant_prompt'])) {
            $prompt = sanitize_textarea_field($_POST['assistant_prompt']);
            update_term_meta($term_id, 'assistant_prompt', $prompt);
        }

        // Save the template ID if selected
        if (isset($_POST['assistant_template'])) {
            $template_id = intval($_POST['assistant_template']);
            update_term_meta($term_id, 'assistant_template_id', $template_id);

            // Save the OpenAI model if provided
            if (isset($_POST['assistant_model'])) {
                $model = sanitize_text_field($_POST['assistant_model']);
                update_term_meta($term_id, 'assistant_model', $model);
            }

            // Save the expertise if provided
            if (isset($_POST['assistant_expertise'])) {
                $expertise = sanitize_text_field($_POST['assistant_expertise']);
                update_term_meta($term_id, 'assistant_expertise', $expertise);
            }

            // Save the categories if provided
            if (isset($_POST['assistant_categories'])) {
                $categories = sanitize_text_field($_POST['assistant_categories']);
                update_term_meta($term_id, 'assistant_categories', $categories);
            }

            // Save the OpenAI assistant ID if provided
            if (isset($_POST['openai_assistant_id'])) {
                $openai_assistant_id = sanitize_text_field($_POST['openai_assistant_id']);
                update_term_meta($term_id, 'openai_assistant_id', $openai_assistant_id);
            }



            // Save the suggested prompts if provided
            if (isset($_POST['assistant_suggested_prompts'])) {
                $suggested_prompts = is_array($_POST['assistant_suggested_prompts']) ? $_POST['assistant_suggested_prompts'] : [];
                update_term_meta($term_id, 'assistant_suggested_prompts', $suggested_prompts);
            }

            // If a template was selected, get its content and update the prompt
            if ($template_id > 0) {
                require_once QKB_PLUGIN_DIR . 'includes/openai/class-qkb-openai-prompt-templates.php';
                $templates_handler = new QKB_OpenAI_Prompt_Templates();
                $template = $templates_handler->get_template($template_id);

                if ($template) {
                    // Get assistant name for template variables
                    $assistant = get_term($term_id, 'kb_assistant');
                    $assistant_name = $assistant ? $assistant->name : 'AI Assistant';

                    // Process template with variables
                    $processed_content = $templates_handler->process_template($template['content'], [
                        'assistant_name' => $assistant_name,
                        'site_name' => get_bloginfo('name')
                    ]);

                    // Update the prompt with the processed template content
                    update_term_meta($term_id, 'assistant_prompt', $processed_content);
                }
            }
        }

        // Save the icon
        if (isset($_POST['assistant_icon'])) {
            $icon = sanitize_text_field($_POST['assistant_icon']);
            update_term_meta($term_id, 'assistant_icon', $icon);
        }
    }

    public function ajax_generate_prompt()
    {
        check_ajax_referer('qkb_generate_prompt', 'nonce');

        $assistant_name = sanitize_text_field($_POST['assistant_name']) ?: 'AI Assistant';
        $assistant_description = sanitize_text_field($_POST['assistant_description']) ?: '';

        $api_key = get_option('qkb_openai_api_key');

        if (!$api_key) {
            wp_send_json_error('API key not configured');
            return;
        }

        $system_prompt = "You are a prompt engineering expert. Create a system prompt for a knowledge base AI assistant that strictly follows these requirements:

1. The assistant must ONLY provide information from content explicitly assigned to it
2. The assistant must maintain clear boundaries about its knowledge scope
3. The assistant must always cite sources when providing information
4. The assistant must format responses in markdown
5. The assistant must acknowledge when information is not in its assigned content

The prompt should define:
- The assistant's role and specialization
- Personality traits and tone of voice
- Response format and citation requirements
- Content boundary enforcement
- Error handling approach";

        $user_prompt = sprintf(
            "Create a comprehensive system prompt for an AI assistant named '%s'%s. The prompt should:

1. Emphasize that the assistant can ONLY access and provide information from content specifically assigned to it
2. Define a professional yet approachable personality
3. Establish clear guidelines for:
   - Citing sources
   - Formatting responses in markdown
   - Handling queries outside its assigned content
   - Maintaining accuracy and avoiding assumptions
4. Include specific instructions for:
   - Response structure
   - Source citation format
   - Content boundary enforcement
   - Error messaging",
            $assistant_name,
            $assistant_description ? " who specializes in " . $assistant_description : ""
        );

        $response = wp_remote_post('https://api.openai.com/v1/chat/completions', [
            'headers' => [
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode([
                'model' => 'gpt-3.5-turbo',
                'messages' => [
                    ['role' => 'system', 'content' => $system_prompt],
                    ['role' => 'user', 'content' => $user_prompt]
                ],
                'temperature' => 0.7,
                'max_tokens' => 500
            ]),
            'timeout' => 15
        ]);

        if (is_wp_error($response)) {
            $fallback_prompt = $this->get_fallback_prompt($assistant_name, $assistant_description);
            wp_send_json_success($fallback_prompt);
            return;
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);

        if (empty($body['choices'][0]['message']['content'])) {
            wp_send_json_error('Invalid response from OpenAI API');
            return;
        }

        $generated_prompt = $body['choices'][0]['message']['content'];
        wp_send_json_success($generated_prompt);
    }

    private function get_fallback_prompt($assistant_name, $assistant_description)
    {
        return sprintf(
            "I am %s, an AI knowledge base assistant%s focused on providing accurate information exclusively from my assigned content.

Personality Traits:
- Professional and courteous in all interactions
- Clear and precise in explanations
- Detail-oriented when providing information
- Patient and helpful with user queries

Core Guidelines:
- I ONLY provide information from content specifically assigned to me
- Every response must include relevant source citations
- All responses are formatted in markdown for clarity
- I maintain strict boundaries about my knowledge scope
- I clearly acknowledge when information is not in my assigned content

Response Structure:
1. Address the query directly
2. Provide information only from assigned content
3. Include markdown formatting for readability
4. Cite sources for all information provided
5. When information is not in my assigned content:
   - Acknowledge this clearly
   - Explain my content boundaries
   - Suggest alternative resources if appropriate

I never:
- Make assumptions about information
- Provide information from unassigned content
- Skip source citations
- Guess or extrapolate beyond my assigned content",
            $assistant_name,
            $assistant_description ? " specializing in " . $assistant_description : ""
        );
    }

    /**
     * Remove the Assistants taxonomy from the admin menu
     * This ensures the taxonomy is still accessible programmatically but not shown in the admin menu
     */
    public function remove_assistants_menu()
    {
        global $submenu;

        // Find and remove the Assistants submenu item
        if (isset($submenu['edit.php?post_type=kb_knowledge_base'])) {
            foreach ($submenu['edit.php?post_type=kb_knowledge_base'] as $key => $item) {
                // The URL for taxonomy pages follows this pattern
                if (isset($item[2]) && strpos($item[2], 'edit-tags.php?taxonomy=kb_assistant') !== false) {
                    unset($submenu['edit.php?post_type=kb_knowledge_base'][$key]);
                    break;
                }
            }
        }
    }

    /**
     * Remove the "Add New Knowledge Base" menu item from the admin menu
     */
    public function remove_add_new_menu()
    {
        global $submenu;

        // Find and remove the "Add New" submenu item
        if (isset($submenu['edit.php?post_type=kb_knowledge_base'])) {
            foreach ($submenu['edit.php?post_type=kb_knowledge_base'] as $key => $item) {
                // The "Add New" menu item typically has "post-new.php?post_type=kb_knowledge_base" in its URL
                if (isset($item[2]) && strpos($item[2], 'post-new.php?post_type=kb_knowledge_base') !== false) {
                    unset($submenu['edit.php?post_type=kb_knowledge_base'][$key]);
                    break;
                }
            }
        }
    }

    public function enqueue_admin_assets($hook)
    {
        if (
            in_array($hook, ['edit-tags.php', 'term.php']) &&
            isset($_GET['taxonomy']) &&
            $_GET['taxonomy'] === 'kb_assistant'
        ) {

            wp_enqueue_style(
                'font-awesome',
                'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css',
                [],
                '6.0.0'
            );
        }

        // Add styles for the embedding status column
        if ($hook === 'edit.php' && isset($_GET['post_type']) && $_GET['post_type'] === 'kb_knowledge_base') {
            wp_add_inline_style('wp-admin', '
                .column-embedding_status {
                    width: 120px;
                }
                .qkb-embedding-status {
                    display: inline-flex;
                    align-items: center;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 12px;
                    line-height: 1;
                    font-weight: 500;
                }
                .qkb-embedding-status.generated {
                    background-color: #e6f6e8;
                    color: #2a8d46;
                }
                .qkb-embedding-status.not-generated {
                    background-color: #f8e8e8;
                    color: #d63638;
                }
                .qkb-embedding-status i {
                    margin-right: 4px;
                }
            ');
        }
    }

    // This method has been moved to the QKB_AJAX_Handlers class
    public function ajax_get_assistant_info()
    {
        check_ajax_referer('qkb_ajax_nonce', 'nonce');

        $assistant_id = isset($_POST['assistant_id']) ? intval($_POST['assistant_id']) : 0;

        if (!$assistant_id) {
            wp_send_json_error('Invalid assistant ID');
            return;
        }

        $assistant = get_term($assistant_id, 'kb_assistant');
        if (is_wp_error($assistant) || !$assistant) {
            wp_send_json_error('assistant not found');
            return;
        }

        // Get assistant meta data
        $icon = get_term_meta($assistant_id, 'assistant_icon', true);
        $description = get_term_meta($assistant_id, 'assistant_description', true) ?: $assistant->description;



        wp_send_json_success([
            'name' => $assistant->name,
            'description' => $description ?: ' I’m here to help with anything you need. Just ask, and I’ll be happy to assist.',
            'icon' => $icon ?: 'fas fa-robot'
        ]);
    }

    public function add_file_upload_meta_box()
    {
        error_log('QKB: Adding file upload meta box');
        add_meta_box(
            'qkb_file_upload',
            __('Document Upload', 'qkb'),
            array($this, 'render_file_upload_meta_box'),
            'kb_knowledge_base',
            'normal',
            'high'
        );
    }

    public function render_file_upload_meta_box($post)
    {
        // Add enctype to the post form
        add_action('post_edit_form_tag', function () {
            echo ' enctype="multipart/form-data"';
        });

        wp_nonce_field('qkb_file_upload', 'qkb_file_upload_nonce');

        $content_source = get_post_meta($post->ID, '_qkb_content_source', true);
        $import_url = get_post_meta($post->ID, '_qkb_import_url', true);
        $uploaded_file = get_post_meta($post->ID, '_qkb_uploaded_file', true);
        ?>
        <div class="qkb-content-source-wrapper">
            <div class="qkb-source-selector">
                <label>
                    <input type="radio" name="qkb_content_source" value="none" <?php checked($content_source, 'none'); ?>         <?php checked($content_source, ''); ?>>
                    <?php _e('Manual Content', 'qkb'); ?>
                </label>
                <label>
                    <input type="radio" name="qkb_content_source" value="file" <?php checked($content_source, 'file'); ?>>
                    <?php _e('Upload Document', 'qkb'); ?>
                </label>
                <label>
                    <input type="radio" name="qkb_content_source" value="url" <?php checked($content_source, 'url'); ?>>
                    <?php _e('Import from URL', 'qkb'); ?>
                </label>
            </div>

            <div class="qkb-file-upload-section" style="display: none;">
                <input type="file" name="qkb_document" id="qkb_document" accept=".pdf,.doc,.docx" />
                <p class="description"><?php _e('Accepted file types: PDF, DOC, DOCX', 'qkb'); ?></p>
                <?php if ($uploaded_file): ?>
                    <div class="qkb-uploaded-file">
                        <p><?php _e('Current file:', 'qkb'); ?>             <?php echo esc_html($uploaded_file['filename']); ?></p>
                    </div>
                <?php endif; ?>
            </div>

            <div class="qkb-url-import-section" style="display: none;">
                <input type="url" name="qkb_import_url" id="qkb_import_url" value="<?php echo esc_attr($import_url); ?>"
                    placeholder="<?php esc_attr_e('Enter URL to import content from', 'qkb'); ?>" class="widefat" />
                <?php if ($import_url): ?>
                    <p class="description"><?php _e('Current source URL:', 'qkb'); ?>
                        <a href="<?php echo esc_url($import_url); ?>" target="_blank"><?php echo esc_html($import_url); ?></a>
                    </p>
                <?php endif; ?>
            </div>
        </div>

        <style>
            .qkb-content-source-wrapper {
                margin: 10px 0;
            }

            .qkb-source-selector {
                margin-bottom: 15px;
            }

            .qkb-source-selector label {
                margin-right: 20px;
            }

            .qkb-file-upload-section,
            .qkb-url-import-section {
                margin-top: 10px;
                padding: 10px;
                background: #f9f9f9;
                border: 1px solid #ddd;
            }
        </style>

        <script>
            jQuery(document).ready(function ($) {
                function updateVisibility() {
                    var selected = $('input[name="qkb_content_source"]:checked').val();
                    $('.qkb-file-upload-section').toggle(selected === 'file');
                    $('.qkb-url-import-section').toggle(selected === 'url');
                }

                $('input[name="qkb_content_source"]').on('change', updateVisibility);
                updateVisibility();
            });
        </script>
        <?php
    }

    public function save_file_upload_meta($post_id)
    {
        // Basic validation
        if (
            get_post_type($post_id) !== 'kb_knowledge_base' ||
            !isset($_POST['qkb_file_upload_nonce']) ||
            !wp_verify_nonce($_POST['qkb_file_upload_nonce'], 'qkb_file_upload') ||
            defined('DOING_AUTOSAVE') && DOING_AUTOSAVE ||
            !current_user_can('edit_post', $post_id)
        ) {
            return;
        }

        // Save content source selection
        $content_source = isset($_POST['qkb_content_source']) ? sanitize_text_field($_POST['qkb_content_source']) : 'none';
        update_post_meta($post_id, '_qkb_content_source', $content_source);

        try {
            if ($content_source === 'url' && !empty($_POST['qkb_import_url'])) {
                $url = esc_url_raw($_POST['qkb_import_url']);
                if (empty($url)) {
                    wp_send_json_error('Invalid URL');
                    return;
                }
                // Process URL content using the import_url_content() function.
                $content = $this->import_url_content($url);
                if (empty($content)) {
                    wp_send_json_error('Failed to import content from the provided URL.');
                    return;
                }
                update_post_meta($post_id, '_qkb_import_url', $url);

            } else if ($content_source === 'file' && !empty($_FILES['qkb_document']['name'])) {
                // Process file upload
                $file = $_FILES['qkb_document'];
                $allowed_types = array(
                    'application/pdf',
                    'application/msword', // .doc
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document' // .docx
                );

                // Get file extension
                $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

                // Validate file extension
                $allowed_extensions = array('pdf', 'doc', 'docx');
                if (!in_array($file_extension, $allowed_extensions)) {
                    throw new Exception('Invalid file type. Please upload PDF, DOC, or DOCX files only.');
                }

                // Handle file upload
                require_once(ABSPATH . 'wp-admin/includes/file.php');
                $upload = wp_handle_upload($file, array('test_form' => false));

                if (!empty($upload['error'])) {
                    throw new Exception($upload['error']);
                }

                // Extract content based on file type
                $content = '';
                if ($file_extension === 'pdf') {
                    $content = $this->extract_pdf_content($upload['file']);
                } else if (in_array($file_extension, ['doc', 'docx'])) {
                    $content = $this->extract_doc_content($upload['file']);
                }

                if (empty($content)) {
                    throw new Exception('Could not extract content from file.');
                }

                // Update post content with plain text
                wp_update_post(array(
                    'ID' => $post_id,
                    'post_content' => $content
                ));

                // Store file metadata
                update_post_meta($post_id, '_qkb_uploaded_file', array(
                    'filename' => $file['name'],
                    'file_path' => $upload['file'],
                    'file_url' => $upload['url'],
                    'file_type' => $file['type'],
                    'upload_date' => current_time('mysql')
                ));
            }

        } catch (Exception $e) {
            error_log('QKB Content Import Error: ' . $e->getMessage());
            wp_die($e->getMessage());
        }
    }

    private function import_url_content($url)
    {
        // Validate URL
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            throw new Exception('Invalid URL provided.');
        }

        // Fetch URL content
        $response = wp_remote_get($url, array(
            'timeout' => 30,
            'user-assistant' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ));

        if (is_wp_error($response)) {
            throw new Exception('Failed to fetch URL content: ' . $response->get_error_message());
        }

        $html = wp_remote_retrieve_body($response);
        if (empty($html)) {
            throw new Exception('No content found at the provided URL.');
        }

        // Load HTML into DOMDocument
        $doc = new DOMDocument();
        @$doc->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'), LIBXML_NOERROR | LIBXML_NOWARNING);
        $xpath = new DOMXPath($doc);

        // Try to find main content using common selectors
        $content_selectors = array(
            "//article[contains(@class, 'content') or contains(@class, 'post') or contains(@class, 'entry')]",
            "//main",
            "//div[contains(@class, 'content') or contains(@class, 'article') or contains(@class, 'post')]",
            "//div[contains(@class, 'entry-content')]",
            "//div[@role='main']",
            "//div[@id='content']",
            "//div[@id='main']"
        );

        $content = '';
        $mainNode = null;

        // Find the main content node
        foreach ($content_selectors as $selector) {
            $nodes = $xpath->query($selector);
            if ($nodes->length > 0) {
                $mainNode = $nodes->item(0);
                break;
            }
        }

        // If no main content found, try body
        if (!$mainNode) {
            $bodies = $xpath->query('//body');
            if ($bodies->length > 0) {
                $mainNode = $bodies->item(0);
            }
        }

        if ($mainNode) {
            // Remove unwanted elements before processing
            $this->removeUnwantedElements($mainNode, $doc);

            // Extract and format the content
            $content = $this->extractStructuredContent($mainNode, $xpath);
        }

        if (empty($content)) {
            throw new Exception('Could not extract meaningful content from the URL.');
        }

        return $content;
    }

    private function removeUnwantedElements($node, $doc)
    {
        // Create a new DOMXPath instance for each query
        $xpath = new DOMXPath($doc);

        // Define unwanted elements
        $unwanted_elements = array(
            '//script',
            '//style',
            '//iframe',
            '//nav',
            '//header',
            '//footer',
            '//form',
            '//aside'
        );

        // Define unwanted classes
        $unwanted_classes = array(
            'sidebar',
            'menu',
            'footer',
            'header',
            'navigation',
            'comment',
            'widget',
            'ad',
            'social'
        );

        // First remove elements by tag name
        foreach ($unwanted_elements as $element) {
            $elements = $xpath->query($element);
            if ($elements) {
                foreach ($elements as $element) {
                    if ($element->parentNode) {
                        $element->parentNode->removeChild($element);
                    }
                }
            }
        }

        // Then remove elements by class
        foreach ($unwanted_classes as $class) {
            // Use a more specific XPath query
            $query = ".//*[contains(concat(' ', normalize-space(@class), ' '), ' $class ')]";
            $elements = $xpath->query($query, $node);
            if ($elements) {
                foreach ($elements as $element) {
                    if ($element->parentNode) {
                        $element->parentNode->removeChild($element);
                    }
                }
            }
        }

        // Remove empty nodes
        $empty_nodes = $xpath->query(".//*[not(normalize-space())]", $node);
        if ($empty_nodes) {
            foreach ($empty_nodes as $empty) {
                if ($empty->parentNode && !in_array(strtolower($empty->nodeName), ['img', 'br', 'hr'])) {
                    $empty->parentNode->removeChild($empty);
                }
            }
        }

        // Remove comments
        $comments = $xpath->query('//comment()', $node);
        if ($comments) {
            foreach ($comments as $comment) {
                if ($comment->parentNode) {
                    $comment->parentNode->removeChild($comment);
                }
            }
        }
    }

    private function extractStructuredContent($node, $xpath)
    {
        $output = '';
        $currentList = null;
        $inTable = false;

        // Process child nodes
        foreach ($node->childNodes as $child) {
            if ($child->nodeType === XML_ELEMENT_NODE) {
                $tagName = strtolower($child->nodeName);
                $text = trim($child->textContent);

                // Skip empty nodes
                if (empty($text)) {
                    continue;
                }

                // Handle different element types
                switch ($tagName) {
                    case 'h1':
                    case 'h2':
                    case 'h3':
                    case 'h4':
                    case 'h5':
                    case 'h6':
                        if ($currentList) {
                            $output .= "<!-- /wp:list -->\n";
                            $currentList = null;
                        }
                        $level = substr($tagName, 1);
                        $output .= sprintf(
                            "<!-- wp:heading {\"level\":%d} -->\n<%s>%s</%s>\n<!-- /wp:heading -->\n\n",
                            $level,
                            $tagName,
                            esc_html($text),
                            $tagName
                        );
                        break;

                    case 'p':
                        if ($currentList) {
                            $output .= "<!-- /wp:list -->\n";
                            $currentList = null;
                        }
                        if (!empty($text)) {
                            $output .= sprintf(
                                "<!-- wp:paragraph -->\n<p>%s</p>\n<!-- /wp:paragraph -->\n\n",
                                esc_html($text)
                            );
                        }
                        break;

                    case 'ul':
                    case 'ol':
                        if ($currentList !== $tagName) {
                            if ($currentList) {
                                $output .= "<!-- /wp:list -->\n";
                            }
                            $output .= sprintf(
                                "<!-- wp:list {\"ordered\":%s} -->\n<%s>\n",
                                ($tagName === 'ol' ? 'true' : 'false'),
                                $tagName
                            );
                            $currentList = $tagName;
                        }
                        $output .= $this->processListItems($child);
                        break;

                    case 'table':
                        if ($currentList) {
                            $output .= "<!-- /wp:list -->\n";
                            $currentList = null;
                        }
                        $output .= $this->processTable($child);
                        break;

                    default:
                        // Handle other elements recursively
                        if (!empty($text)) {
                            $output .= $this->extractStructuredContent($child, $xpath);
                        }
                        break;
                }
            }
        }

        // Close any open lists
        if ($currentList) {
            $output .= "<!-- /wp:list -->\n</{$list_type}>\n\n";
        }

        return $output;
    }

    private function processListItems($list)
    {
        $output = '';
        foreach ($list->childNodes as $item) {
            if ($item->nodeName === 'li') {
                $text = trim($item->textContent);
                if (!empty($text)) {
                    $output .= sprintf("<li>%s</li>\n", esc_html($text));
                }
            }
        }
        return $output;
    }

    private function processTable($table)
    {
        $output = "<!-- wp:table -->\n<table class=\"wp-block-table\"><tbody>\n";

        foreach ($table->childNodes as $row) {
            if ($row->nodeName === 'tr') {
                $output .= "<tr>\n";
                foreach ($row->childNodes as $cell) {
                    if (in_array($cell->nodeName, ['td', 'th'])) {
                        $text = trim($cell->textContent);
                        $output .= sprintf(
                            "<%s>%s</%s>\n",
                            $cell->nodeName,
                            esc_html($text),
                            $cell->nodeName
                        );
                    }
                }
                $output .= "</tr>\n";
            }
        }

        $output .= "</tbody></table>\n<!-- /wp:table -->\n\n";
        return $output;
    }

    private function extract_pdf_content($file_path)
    {
        require_once QKB_PLUGIN_DIR . 'includes/class-qkb-document-extractor.php';

        try {
            $extractor = new QKB_Document_Extractor();
            $result = $extractor->extract($file_path, 'pdf');

            if (!empty($result['error'])) {
                throw new Exception($result['error']);
            }

            // Check if we have content
            if (empty($result['content'])) {
                // Fallback to the old method if the new one fails
                require_once QKB_PLUGIN_DIR . 'vendor/autoload.php';
                $parser = new \Smalot\PdfParser\Parser();
                $pdf = $parser->parseFile($file_path);
                $content = '';

                // Process each page
                foreach ($pdf->getPages() as $page) {
                    $text = $page->getText();
                    $content .= $text . "\n\n";
                }

                // Format the content for WordPress
                $content = $this->format_extracted_content($content);
                return $content;
            }

            // Format the content for WordPress
            return $this->format_extracted_content($result['content']);

        } catch (Exception $e) {
            error_log('PDF Extraction Error: ' . $e->getMessage());
            throw new Exception('Failed to extract content from PDF.');
        }
    }

    private function extract_doc_content($file_path)
    {
        require_once QKB_PLUGIN_DIR . 'includes/class-qkb-document-extractor.php';

        try {
            $extractor = new QKB_Document_Extractor();
            $file_type = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));
            $result = $extractor->extract($file_path, $file_type);

            if (!empty($result['error'])) {
                throw new Exception($result['error']);
            }

            // Check if we have content
            if (empty($result['content'])) {
                // Fallback to the old method if the new one fails
                require_once QKB_PLUGIN_DIR . 'vendor/autoload.php';
                $content = '';
                $phpWord = \PhpOffice\PhpWord\IOFactory::load($file_path);

                // Process each section
                foreach ($phpWord->getSections() as $section) {
                    foreach ($section->getElements() as $element) {
                        if ($element instanceof \PhpOffice\PhpWord\Element\TextRun) {
                            foreach ($element->getElements() as $textElement) {
                                if ($textElement instanceof \PhpOffice\PhpWord\Element\Text) {
                                    $content .= $textElement->getText();
                                }
                            }
                            $content .= "\n";
                        } elseif ($element instanceof \PhpOffice\PhpWord\Element\Text) {
                            $content .= $element->getText() . "\n";
                        } elseif ($element instanceof \PhpOffice\PhpWord\Element\Table) {
                            foreach ($element->getRows() as $row) {
                                $rowContent = [];
                                foreach ($row->getCells() as $cell) {
                                    $cellContent = '';
                                    foreach ($cell->getElements() as $cellElement) {
                                        if ($cellElement instanceof \PhpOffice\PhpWord\Element\TextRun) {
                                            foreach ($cellElement->getElements() as $textElement) {
                                                if ($textElement instanceof \PhpOffice\PhpWord\Element\Text) {
                                                    $cellContent .= $textElement->getText();
                                                }
                                            }
                                        }
                                    }
                                    $rowContent[] = trim($cellContent);
                                }
                                $content .= implode("\t", $rowContent) . "\n";
                            }
                        }
                    }
                    $content .= "\n";
                }

                // Format the content for WordPress
                $content = $this->format_extracted_content($content);
                return $content;
            }

            // Format the content for WordPress
            return $this->format_extracted_content($result['content']);

        } catch (Exception $e) {
            error_log('DOC/DOCX Extraction Error: ' . $e->getMessage());
            throw new Exception('Failed to extract content from document.');
        }
    }

    /**
     * Format extracted document content for better display in WordPress
     *
     * @param string $content Raw extracted content
     * @return string Formatted content
     */
    private function format_extracted_content($content)
    {
        if (empty($content)) {
            return '';
        }

        // Remove excessive whitespace and normalize line breaks
        $content = preg_replace('/\s+/', ' ', $content);
        $content = preg_replace('/\n\s*\n+/', "\n\n", $content);

        // Identify potential paragraphs and headings
        $lines = explode("\n", $content);
        $formatted = '';
        $in_list = false;

        foreach ($lines as $line) {
            $line = trim($line);

            if (empty($line)) {
                $formatted .= "\n\n";
                $in_list = false;
                continue;
            }

            // Check if line looks like a heading
            if (preg_match('/^[A-Z0-9][\s\w\d\.\-]{1,60}$/', $line) && strlen($line) < 100) {
                // Looks like a heading
                $formatted .= "\n\n<h3>" . esc_html($line) . "</h3>\n\n";
                $in_list = false;
            }
            // Check if line looks like a list item
            elseif (preg_match('/^[\*\-\•\○\◦\d]+[\.\)\]]*\s+/', $line)) {
                if (!$in_list) {
                    $formatted .= "\n<ul>\n";
                    $in_list = true;
                }
                $formatted .= "<li>" . esc_html(preg_replace('/^[\*\-\•\○\◦\d]+[\.\)\]]*\s+/', '', $line)) . "</li>\n";
            }
            // Regular paragraph
            else {
                if ($in_list) {
                    $formatted .= "</ul>\n\n";
                    $in_list = false;
                }
                $formatted .= "<p>" . esc_html($line) . "</p>\n\n";
            }
        }

        // Close any open list
        if ($in_list) {
            $formatted .= "</ul>\n";
        }

        return $formatted;
    }


    public function search($query, $params = [])
    {
        global $wpdb;

        // Default search parameters
        $defaults = [
            'max_results' => 5,
            'categories' => [],
            'content_types' => ['kb_knowledge_base', 'kb_external_content']
        ];

        // Merge with provided parameters
        $params = wp_parse_args($params, $defaults);

        error_log('Searching knowledge base with query: ' . $query);
        error_log('Search parameters: ' . print_r($params, true));

        // Prepare search terms
        $search_terms = explode(' ', $query);
        $search_terms = array_filter($search_terms, function ($term) {
            return strlen($term) > 2;
        });

        // Build the base query
        $select = "SELECT DISTINCT p.ID, p.post_title, p.post_content, p.post_type,";

        // Add relevance scoring
        $relevance_parts = [];
        foreach ($search_terms as $term) {
            $like_term = '%' . $wpdb->esc_like($term) . '%';
            $relevance_parts[] = $wpdb->prepare(
                "IF(p.post_title LIKE %s, 10, 0) + " .
                "IF(p.post_content LIKE %s, 5, 0)",
                $like_term,
                $like_term
            );
        }
        $select .= " (" . implode(" + ", $relevance_parts) . ") as relevance";

        $from = " FROM {$wpdb->posts} p";

        // Add joins for assistant-specific content filtering
        $from .= " LEFT JOIN {$wpdb->term_relationships} tr ON p.ID = tr.object_id";
        $from .= " LEFT JOIN {$wpdb->term_taxonomy} tt ON tr.term_taxonomy_id = tt.term_taxonomy_id";
        $from .= " LEFT JOIN {$wpdb->terms} t ON tt.term_id = t.term_id";

        // Build WHERE clause
        $where = " WHERE p.post_status = 'publish'";

        // Add post type filter
        $post_types = array_map(function ($type) use ($wpdb) {
            return $wpdb->prepare('%s', $type);
        }, $params['content_types']);
        $where .= " AND p.post_type IN (" . implode(',', $post_types) . ")";

        // Add assistant-specific content filter
        if (!empty($params['assistant_id'])) {
            error_log('Filtering by assistant ID: ' . $params['assistant_id'] . ' (type: ' . gettype($params['assistant_id']) . ')');

            // Get the term ID directly to ensure we're using the correct value
            $assistant_term = get_term($params['assistant_id'], 'kb_assistant');
            if (!is_wp_error($assistant_term) && $assistant_term) {
                error_log('Found assistant term: ' . $assistant_term->name . ' (ID: ' . $assistant_term->term_id . ')');

                $where .= $wpdb->prepare(
                    " AND EXISTS (
                        SELECT 1 FROM {$wpdb->term_relationships} tr2
                        JOIN {$wpdb->term_taxonomy} tt2 ON tr2.term_taxonomy_id = tt2.term_taxonomy_id
                        WHERE tr2.object_id = p.ID
                        AND tt2.taxonomy = 'kb_assistant'
                        AND tt2.term_id = %d
                    )",
                    $assistant_term->term_id
                );
            } else {
                error_log('Assistant term not found for ID: ' . $params['assistant_id']);
            }
        }

        // Add category filter if specified
        if (!empty($params['categories'])) {
            $category_ids = array_map('intval', $params['categories']);
            $where .= " AND tt.taxonomy = 'kb_category'";
            $where .= " AND tt.term_id IN (" . implode(',', $category_ids) . ")";
        }

        // Add search terms
        $search_conditions = [];
        foreach ($search_terms as $term) {
            $like_term = '%' . $wpdb->esc_like($term) . '%';
            $search_conditions[] = $wpdb->prepare(
                "(p.post_title LIKE %s OR p.post_content LIKE %s)",
                $like_term,
                $like_term
            );
        }
        if (!empty($search_conditions)) {
            $where .= " AND (" . implode(" OR ", $search_conditions) . ")";
        }

        // Complete the query
        $query = $select . $from . $where . " ORDER BY relevance DESC LIMIT " . intval($params['max_results']);

        error_log('Executing SQL query: ' . $query);

        $results = $wpdb->get_results($query);
        error_log('Found ' . count($results) . ' results');

        $context = '';
        $sources = [];

        foreach ($results as $result) {
            // Skip if relevance score is too low
            if ($result->relevance < 5) {
                continue;
            }

            // Clean and format content
            $content = wp_strip_all_tags($result->post_content);
            $content = preg_replace('/\s+/', ' ', $content);

            // Extract relevant snippet
            $snippet = $this->extract_relevant_snippet($content, $search_terms);

            // Add to context with clear structure
            $context .= "Source: {$result->post_title}\n\n";
            $context .= "Relevance Score: {$result->relevance}\n\n";
            $context .= "Content: {$snippet}\n\n";
            $context .= "---\n\n";

            // Add to sources
            $sources[] = [
                'title' => $result->post_title,
                'url' => get_permalink($result->ID),
                'type' => $result->post_type,
                'relevance' => $result->relevance
            ];
        }

        error_log('Compiled context length: ' . strlen($context));
        error_log('Number of sources: ' . count($sources));

        return [
            'context' => $context,
            'sources' => $sources
        ];
    }

    private function extract_relevant_snippet($content, $search_terms, $snippet_length = 300)
    {
        // Find the position of the first search term
        $first_pos = strlen($content);
        foreach ($search_terms as $term) {
            $pos = stripos($content, $term);
            if ($pos !== false && $pos < $first_pos) {
                $first_pos = $pos;
            }
        }

        // Calculate snippet start and end positions
        $start = max(0, $first_pos - ($snippet_length / 2));
        $end = min(strlen($content), $start + $snippet_length);

        // Adjust start to not break words
        while ($start > 0 && $content[$start] != ' ') {
            $start--;
        }

        // Adjust end to not break words
        while ($end < strlen($content) && $content[$end] != ' ') {
            $end++;
        }

        // Extract and format snippet
        $snippet = substr($content, $start, $end - $start);

        // Add ellipsis if needed
        if ($start > 0) {
            $snippet = '...' . $snippet;
        }
        if ($end < strlen($content)) {
            $snippet = $snippet . '...';
        }

        // Highlight search terms
        foreach ($search_terms as $term) {
            $snippet = preg_replace(
                '/(' . preg_quote($term, '/') . ')/i',
                '**$1**',
                $snippet
            );
        }

        return $snippet;
    }

    public function verify_knowledge_base()
    {
        global $wpdb;

        $issues = [];

        // Check if knowledge base posts exist
        $count = $wpdb->get_var("
            SELECT COUNT(*)
            FROM {$wpdb->posts}
            WHERE post_type = 'kb_knowledge_base'
            AND post_status = 'publish'
        ");

        error_log('Found ' . $count . ' knowledge base articles');

        if ($count == 0) {
            $issues[] = 'No published knowledge base articles found';
        }

        // Check if categories are set up
        $term_count = $wpdb->get_var("
            SELECT COUNT(*)
            FROM {$wpdb->terms} t
            JOIN {$wpdb->term_taxonomy} tt ON t.term_id = tt.term_id
            WHERE tt.taxonomy = 'kb_category'
        ");

        error_log('Found ' . $term_count . ' knowledge base categories');

        if ($term_count == 0) {
            $issues[] = 'No knowledge base categories found';
        }

        return [
            'status' => empty($issues) ? 'ok' : 'issues',
            'issues' => $issues,
            'kb_count' => $count,
            'category_count' => $term_count
        ];
    }

    /**
     * Search knowledge base titles
     *
     * @param string $query Search query
     * @param int $limit Maximum number of results
     * @return array Array of post objects
     */
    public function search_titles($query, $limit = 5)
    {
        global $wpdb;

        // Sanitize inputs
        $query = sanitize_text_field($query);
        $limit = absint($limit);

        try {
            // Search in post titles
            $results = $wpdb->get_results(
                $wpdb->prepare(
                    "SELECT ID, post_title, post_type
                    FROM {$wpdb->posts}
                    WHERE post_type IN ('kb_knowledge_base', 'post', 'page')
                    AND post_status = 'publish'
                    AND (
                        post_title LIKE %s
                        OR post_title LIKE %s
                        OR post_title LIKE %s
                    )
                    ORDER BY
                        CASE
                            WHEN post_title LIKE %s THEN 1
                            WHEN post_title LIKE %s THEN 2
                            ELSE 3
                        END,
                        post_title
                    LIMIT %d",
                    $wpdb->esc_like($query) . '%',      // Starts with query
                    '% ' . $wpdb->esc_like($query) . '%', // Contains query as word
                    '%' . $wpdb->esc_like($query) . '%',  // Contains query
                    $wpdb->esc_like($query) . '%',      // Order by starts with
                    '% ' . $wpdb->esc_like($query) . '%', // Order by contains word
                    $limit
                )
            );

            if ($wpdb->last_error) {
                error_log('KB search error: ' . $wpdb->last_error);
                return [];
            }

            // Filter out any unwanted post types and ensure we have valid results
            return array_filter($results, function ($post) {
                return !empty($post->ID) && !empty($post->post_title);
            });

        } catch (Exception $e) {
            error_log('Error in KB search_titles: ' . $e->getMessage());
            return [];
        }
    }

    public function handle_knowledge_submission()
    {
        check_ajax_referer('qkb_ajax_nonce', 'nonce');

        // Check if user has permission to add knowledge
        $allowed_roles = get_option('qkb_add_knowledge_roles', []);
        $user = wp_get_current_user();
        $user_roles = (array) $user->roles;

        if (empty(array_intersect($user_roles, $allowed_roles))) {
            wp_send_json_error('Permission denied');
            return;
        }

        $title = sanitize_text_field($_POST['title'] ?? '');
        $content_type = sanitize_text_field($_POST['content_type'] ?? '');
        $assistant_id = intval($_POST['assistant_id'] ?? 0);

        if (empty($title) || empty($content_type) || empty($assistant_id)) {
            wp_send_json_error('Missing required fields');
            return;
        }

        $content = '';
        $uploaded_meta = array(); // to hold file meta if uploaded

        switch ($content_type) {
            case 'text':
                $content = wp_kses_post($_POST['content'] ?? '');
                break;

            case 'file':
                if (!isset($_FILES['file']) || empty($_FILES['file']['name'])) {
                    wp_send_json_error('No file uploaded');
                    return;
                }

                $file = $_FILES['file'];
                $allowed_extensions = array('pdf', 'doc', 'docx');
                $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

                if (!in_array($file_extension, $allowed_extensions)) {
                    wp_send_json_error('Invalid file type. Only PDF, DOC, and DOCX files are allowed.');
                    return;
                }

                require_once(ABSPATH . 'wp-admin/includes/file.php');
                $upload = wp_handle_upload($file, array('test_form' => false));

                if (!empty($upload['error'])) {
                    wp_send_json_error($upload['error']);
                    return;
                }

                // Extract content based on file type
                if ($file_extension === 'pdf') {
                    $content = $this->extract_pdf_content($upload['file']);
                } else if (in_array($file_extension, array('doc', 'docx'))) {
                    $content = $this->extract_doc_content($upload['file']);
                }

                if (empty($content)) {
                    wp_send_json_error('Could not extract content from the uploaded file.');
                    return;
                }

                // Store file metadata for saving with the post
                $uploaded_meta = array(
                    'filename' => $file['name'],
                    'file_path' => $upload['file'],
                    'file_url' => $upload['url'],
                    'file_type' => $file['type'],
                    'upload_date' => current_time('mysql')
                );
                break;

            case 'url':
                $url = esc_url_raw($_POST['url'] ?? '');
                if (empty($url)) {
                    wp_send_json_error('Invalid URL');
                    return;
                }
                // Process URL content using the import_url_content() function.
                $content = $this->import_url_content($url);
                if (empty($content)) {
                    wp_send_json_error('Failed to import content from the provided URL.');
                    return;
                }
                break;

            default:
                wp_send_json_error('Invalid content type');
                return;
        }

        $post_data = array(
            'post_title' => $title,
            'post_content' => $content,
            'post_type' => 'kb_knowledge_base',
            'post_status' => 'publish',
            'post_author' => get_current_user_id()
        );

        $post_id = wp_insert_post($post_data);

        if (is_wp_error($post_id)) {
            wp_send_json_error('Failed to add knowledge');
            return;
        }

        wp_set_object_terms($post_id, [$assistant_id], 'kb_assistant');
        update_post_meta($post_id, 'content_type', $content_type);
        if (!empty($uploaded_meta)) {
            update_post_meta($post_id, '_qkb_uploaded_file', $uploaded_meta);
        }

        // Generate embedding for the new post
        $this->generate_post_embedding($post_id, get_post($post_id), true);

        wp_send_json_success('Content submitted successfully');
    }

    // --- New Method: Delete a Knowledge Entry ---
    public function ajax_delete_knowledge()
    {
        check_ajax_referer('qkb_ajax_nonce', 'nonce');

        if (!current_user_can('delete_posts')) {
            wp_send_json_error('Permission denied');
            return;
        }

        $knowledge_id = isset($_POST['knowledge_id']) ? intval($_POST['knowledge_id']) : 0;
        if (!$knowledge_id) {
            wp_send_json_error('Invalid knowledge ID');
            return;
        }

        $deleted = wp_delete_post($knowledge_id, true);
        if (!$deleted) {
            wp_send_json_error('Unable to delete knowledge entry');
            return;
        }

        wp_send_json_success('Knowledge entry deleted');
    }

    // --- New Method: List Knowledge Entries ---
    public function ajax_list_knowledge()
    {
        check_ajax_referer('qkb_ajax_nonce', 'nonce');

        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Permission denied');
            return;
        }

        $args = [
            'post_type' => 'kb_knowledge_base',
            'post_status' => 'publish',
            'posts_per_page' => -1,
        ];

        $query = new WP_Query($args);
        $data = [];
        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $data[] = [
                    'id' => get_the_ID(),
                    'title' => get_the_title(),
                    'date' => get_the_date(),
                ];
            }
        }
        wp_reset_postdata();
        wp_send_json_success($data);
    }

    private function get_default_prompt($assistant)
    {
        // Get assistant description and metadata
        $description = $assistant->description;
        $expertise = get_term_meta($assistant->term_id, 'assistant_expertise', true);
        $categories = get_term_meta($assistant->term_id, 'assistant_categories', true);
        $icon = get_term_meta($assistant->term_id, 'assistant_icon', true) ?: 'fas fa-robot';

        return "# {$assistant->name} - Knowledge Base Specialist

## Content Authority
- Primary Source: Assigned knowledge base content only
- Secondary Source: General knowledge for context only
- Fact Checking: Always verify against assigned content
- Response Basis: Must cite specific knowledge base sources

## Expertise Focus
- Domain: " . ($description ?: 'General Knowledge Support') . "
- Specialization: " . ($expertise ?: 'Knowledge Base Management') . "
- Categories: " . ($categories ? implode(', ', (array) $categories) : 'All Categories') . "

## Content Verification Protocol
1. Source Validation
   - Check assigned knowledge base first
   - Verify information accuracy
   - Cross-reference multiple sources
   - Flag any content gaps

2. Response Construction
   - Quote relevant sections directly
   - Provide clear source citations
   - Structure information hierarchically
   - Highlight verified content

3. Knowledge Boundaries
   - Clearly state content limitations
   - Distinguish between verified and general knowledge
   - Redirect to appropriate knowledge base sections
   - Report missing information

## Response Requirements
1. Source Attribution
   - Include specific document references
   - Cite relevant sections
   - Link to source materials
   - Maintain content traceability

2. Content Accuracy
   - Use exact quotes when possible
   - Maintain original context
   - Avoid assumptions
   - Flag uncertainties

3. Information Gaps
   - Acknowledge missing content
   - Suggest knowledge base updates
   - Recommend alternative sources
   - Track frequent gaps

## Quality Controls
1. Verification Steps
   - Check primary sources
   - Validate against knowledge base
   - Confirm current versions
   - Note last update dates

2. Response Validation
   - Review source alignment
   - Verify citation accuracy
   - Ensure content relevance
   - Check response completeness

3. Content Management
   - Flag outdated information
   - Identify content gaps
   - Suggest updates
   - Track common queries

## Interaction Rules
1. Content Boundaries
   - Stay within assigned content
   - Acknowledge limitations clearly
   - Redirect to verified sources
   - Report knowledge gaps

2. Response Format
   - Use consistent citation format
   - Structure with markdown
   - Include source links
   - Maintain clarity

3. Follow-up Protocol
   - Suggest related content
   - Reference similar topics
   - Guide to additional resources
   - Track user needs";
    }
    /**
     * Generate embedding for a knowledge base post
     *
     * @param int $post_id The post ID
     * @param WP_Post $post The post object
     * @param bool $is_new Whether this is a new post
     * @return bool|WP_Error True on success, WP_Error on failure
     */
    public function generate_post_embedding($post_id, $post, $is_new = false)
    {
        // Skip revisions and auto-saves
        if (wp_is_post_revision($post_id) || wp_is_post_autosave($post_id)) {
            return false;
        }

        // Skip if not a knowledge base post
        if ($post->post_type !== 'kb_knowledge_base') {
            return false;
        }

        // Skip if post is not published
        if ($post->post_status !== 'publish') {
            return false;
        }

        try {
            // Get post content for embedding
            $content = $post->post_title . "\n\n" . $post->post_content;

            // Initialize OpenAI handler
            require_once QKB_PLUGIN_DIR . 'includes/class-qkb-openai-handler.php';
            $openai = new QKB_OpenAI_Handler();

            // Truncate content if it's too long
            // OpenAI's embedding models have a token limit (8191 for text-embedding-3-small)
            // A rough estimate is that 1 token ≈ 4 characters in English
            $max_chars = 8000 * 4; // Conservative limit to stay under token limit

            if (mb_strlen($content) > $max_chars) {
                error_log('Content too long for embedding, truncating: ' . $post_id);
                // Take the first part (title and beginning) and the last part (conclusion)
                $first_part = mb_substr($content, 0, $max_chars * 0.7); // 70% from the beginning
                $last_part = mb_substr($content, -($max_chars * 0.3)); // 30% from the end
                $content = $first_part . "\n\n[...content truncated...]\n\n" . $last_part;
            }

            // Generate embedding
            $embedding = $openai->get_embedding($content);

            if (is_wp_error($embedding)) {
                error_log('Error generating embedding: ' . $embedding->get_error_message());
                return $embedding;
            }

            // Save embedding to post meta
            update_post_meta($post_id, '_qkb_embedding', $embedding);
            update_post_meta($post_id, '_qkb_embedding_updated', current_time('mysql'));

            return true;
        } catch (Exception $e) {
            error_log('Exception generating embedding: ' . $e->getMessage());
            return new WP_Error('embedding_error', $e->getMessage());
        }
    }

    /**
     * Add embedding status column to the Knowledge Base admin list table
     *
     * @param array $columns The existing columns
     * @return array The modified columns
     */
    public function add_embedding_column($columns)
    {
        // Check if the embedding_status column already exists
        if (isset($columns['embedding_status'])) {
            return $columns;
        }

        $new_columns = array();

        // Insert the embedding status column after the title column
        foreach ($columns as $key => $value) {
            $new_columns[$key] = $value;
            if ($key === 'title') {
                $new_columns['embedding_status'] = __('Embedding', 'q-knowledge-base');
            }
        }

        return $new_columns;
    }

    /**
     * Render the embedding status column content
     *
     * @param string $column_name The name of the column
     * @param int $post_id The post ID
     */
    public function render_embedding_column($column_name, $post_id)
    {
        static $processed_posts = array();

        if ($column_name !== 'embedding_status') {
            return;
        }

        // Check if we've already processed this post for this column
        if (in_array($post_id, $processed_posts)) {
            return;
        }

        // Mark this post as processed
        $processed_posts[] = $post_id;

        $embedding = get_post_meta($post_id, '_qkb_embedding', true);
        $updated = get_post_meta($post_id, '_qkb_embedding_updated', true);

        if (!empty($embedding)) {
            echo '<span class="qkb-embedding-status generated"><i class="dashicons dashicons-yes"></i> ' . __('Generated', 'q-knowledge-base') . '</span>';
            if (!empty($updated)) {
                echo '<br><small>' . sprintf(__('Updated: %s', 'q-knowledge-base'), date_i18n(get_option('date_format'), strtotime($updated))) . '</small>';
            }
        } else {
            echo '<span class="qkb-embedding-status not-generated"><i class="dashicons dashicons-no"></i> ' . __('Not Generated', 'q-knowledge-base') . '</span>';
            echo '<br><a href="#" class="generate-embedding" data-post-id="' . esc_attr($post_id) . '">' . __('Generate', 'q-knowledge-base') . '</a>';

            // Add inline script for the generate link
            $generated_text = esc_js(__('Generated', 'q-knowledge-base'));
            $updated_text = esc_js(sprintf(__('Updated: %s', 'q-knowledge-base'), date_i18n(get_option('date_format'))));
            $generating_text = esc_js(__('Generating...', 'q-knowledge-base'));
            $error_text = esc_js(__('Error - Try Again', 'q-knowledge-base'));
            $nonce = wp_create_nonce('qkb_ajax_nonce');

            echo "<script>
                jQuery(document).ready(function($) {
                    $(\".generate-embedding[data-post-id={$post_id}]\").on(\"click\", function(e) {
                        e.preventDefault();
                        const link = $(this);
                        const postId = link.data(\"post-id\");

                        link.text(\"{$generating_text}\").addClass(\"generating\");

                        $.ajax({
                            url: ajaxurl,
                            type: \"POST\",
                            data: {
                                action: \"qkb_generate_embedding\",
                                post_id: postId,
                                nonce: \"{$nonce}\"
                            },
                            success: function(response) {
                                if (response.success) {
                                    const successHtml = '<span class=\"qkb-embedding-status generated\"><i class=\"dashicons dashicons-yes\"></i> {$generated_text}</span><br><small>{$updated_text}</small>';
                                    link.closest(\"td\").html(successHtml);
                                } else {
                                    link.text(\"{$error_text}\").removeClass(\"generating\");
                                }
                            },
                            error: function() {
                                link.text(\"{$error_text}\").removeClass(\"generating\");
                            }
                        });
                    });
                });
            </script>";
        }
    }

    /**
     * Make the embedding status column sortable
     *
     * @param array $columns The sortable columns
     * @return array The modified sortable columns
     */
    public function make_embedding_column_sortable($columns)
    {
        $columns['embedding_status'] = 'embedding_status';
        return $columns;
    }

    /**
     * Handle the sorting of the embedding status column
     *
     * @param WP_Query $query The WordPress query object
     */
    public function embedding_column_orderby($query)
    {
        if (!is_admin() || !$query->is_main_query()) {
            return;
        }

        $orderby = $query->get('orderby');

        if ('embedding_status' === $orderby) {
            $query->set('meta_key', '_qkb_embedding');
            $query->set('orderby', 'meta_value');
        }
    }

    /**
     * Add meta box for embedding management
     */
    public function add_embedding_meta_box()
    {
        add_meta_box(
            'qkb_embedding_meta_box',
            __('Knowledge Base Embedding', 'q-knowledge-base'),
            array($this, 'render_embedding_meta_box'),
            'kb_knowledge_base',
            'side',
            'default'
        );
    }

    /**
     * Render the embedding meta box
     *
     * @param WP_Post $post The post object
     */
    public function render_embedding_meta_box($post)
    {
        $embedding = get_post_meta($post->ID, '_qkb_embedding', true);
        $updated = get_post_meta($post->ID, '_qkb_embedding_updated', true);

        wp_nonce_field('qkb_generate_embedding_nonce', 'qkb_generate_embedding_nonce');

        echo '<div class="qkb-embedding-info">';

        if (!empty($embedding)) {
            echo '<p>' . __('Embedding Status:', 'q-knowledge-base') . ' <span class="qkb-status qkb-status-success">' . __('Generated', 'q-knowledge-base') . '</span></p>';

            if (!empty($updated)) {
                echo '<p>' . __('Last Updated:', 'q-knowledge-base') . ' ' . date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($updated)) . '</p>';
            }
        } else {
            echo '<p>' . __('Embedding Status:', 'q-knowledge-base') . ' <span class="qkb-status qkb-status-warning">' . __('Not Generated', 'q-knowledge-base') . '</span></p>';
        }

        echo '<button type="button" class="button qkb-generate-embedding" data-post-id="' . esc_attr($post->ID) . '">';
        echo '<span class="dashicons dashicons-update"></span> ' . __('Generate Embedding', 'q-knowledge-base');
        echo '</button>';

        echo '<p class="description">' . __('Embeddings are used for semantic search in the knowledge base.', 'q-knowledge-base') . '</p>';
        echo '</div>';

        // Add inline styles
        echo '<style>
            .qkb-embedding-info {
                margin: 10px 0;
            }
            .qkb-status {
                font-weight: bold;
            }
            .qkb-status-success {
                color: #46b450;
            }
            .qkb-status-warning {
                color: #ffb900;
            }
            .qkb-generate-embedding {
                margin: 10px 0;
                display: flex;
                align-items: center;
            }
            .qkb-generate-embedding .dashicons {
                margin-right: 5px;
            }
        </style>';

        // Add JavaScript for AJAX
        echo '<script>
            jQuery(document).ready(function($) {
                $(".qkb-generate-embedding").on("click", function() {
                    const button = $(this);
                    const postId = button.data("post-id");

                    button.prop("disabled", true);
                    button.find(".dashicons").addClass("dashicons-update-spin");

                    $.ajax({
                        url: ajaxurl,
                        type: "POST",
                        data: {
                            action: "qkb_generate_embedding",
                            post_id: postId,
                            nonce: "' . wp_create_nonce('qkb_ajax_nonce') . '"
                        },
                        success: function(response) {
                            if (response.success) {
                                $(".qkb-status").removeClass("qkb-status-warning").addClass("qkb-status-success").text("Generated");
                                location.reload();
                            } else {
                                alert("Error: " + response.data);
                            }
                        },
                        error: function() {
                            alert("Error generating embedding. Please try again.");
                        },
                        complete: function() {
                            button.prop("disabled", false);
                            button.find(".dashicons").removeClass("dashicons-update-spin");
                        }
                    });
                });
            });
        </script>';
    }

    /**
     * AJAX handler for generating embedding for a single post
     */
    public function ajax_generate_embedding()
    {
        check_ajax_referer('qkb_ajax_nonce', 'nonce');

        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Permission denied');
            return;
        }

        $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
        if (!$post_id) {
            wp_send_json_error('Invalid post ID');
            return;
        }

        $post = get_post($post_id);
        if (!$post || $post->post_type !== 'kb_knowledge_base') {
            wp_send_json_error('Invalid knowledge base post');
            return;
        }

        $result = $this->generate_post_embedding($post_id, $post, false);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
            return;
        }

        wp_send_json_success('Embedding generated successfully');
    }

    /**
     * AJAX handler for generating embeddings for all knowledge base posts
     */
    public function ajax_generate_all_embeddings()
    {
        check_ajax_referer('qkb_ajax_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
            return;
        }

        $args = array(
            'post_type' => 'kb_knowledge_base',
            'post_status' => 'publish',
            'posts_per_page' => -1,
        );

        $query = new WP_Query($args);
        $total = $query->found_posts;
        $processed = 0;
        $success = 0;
        $errors = array();

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $post_id = get_the_ID();
                $post = get_post($post_id);

                $result = $this->generate_post_embedding($post_id, $post, false);
                $processed++;

                if (is_wp_error($result)) {
                    $errors[] = array(
                        'id' => $post_id,
                        'title' => get_the_title(),
                        'error' => $result->get_error_message()
                    );
                } else {
                    $success++;
                }
            }
        }
        wp_reset_postdata();

        wp_send_json_success(array(
            'total' => $total,
            'processed' => $processed,
            'success' => $success,
            'errors' => $errors
        ));
    }

    /**
     * Get similar articles by embedding
     *
     * @param string $query The query text
     * @param int $limit Maximum number of results
     * @param float $min_similarity Minimum similarity score (0-1)
     * @param int $assistant_id Optional assistant ID to filter results
     * @return array Array of similar articles
     */
    public function get_similar_articles_by_embedding($query, $limit = 5, $min_similarity = 0.7, $assistant_id = null)
    {
        try {
            // Initialize OpenAI handler
            require_once QKB_PLUGIN_DIR . 'includes/class-qkb-openai-handler.php';
            $openai = new QKB_OpenAI_Handler();

            // Generate embedding for the query
            $query_embedding = $openai->get_embedding($query);

            if (is_wp_error($query_embedding)) {
                error_log('Error generating query embedding: ' . $query_embedding->get_error_message());
                return array();
            }

            // Get all knowledge base posts with embeddings
            $args = array(
                'post_type' => array('kb_knowledge_base', 'kb_external_content'),
                'post_status' => 'publish',
                'posts_per_page' => -1,
                'meta_query' => array(
                    array(
                        'key' => '_qkb_embedding',
                        'compare' => 'EXISTS'
                    )
                )
            );

            // Add assistant filter if specified
            if (!empty($assistant_id)) {
                error_log('Filtering similar articles by assistant ID: ' . $assistant_id . ' (type: ' . gettype($assistant_id) . ')');

                // Get the term ID directly to ensure we're using the correct value
                $assistant_term = get_term($assistant_id, 'kb_assistant');
                if (!is_wp_error($assistant_term) && $assistant_term) {
                    error_log('Found assistant term for similar articles: ' . $assistant_term->name . ' (ID: ' . $assistant_term->term_id . ')');

                    $args['tax_query'] = array(
                        array(
                            'taxonomy' => 'kb_assistant',
                            'field' => 'term_id',
                            'terms' => intval($assistant_term->term_id)
                        )
                    );
                } else {
                    error_log('Assistant term not found for similar articles with ID: ' . $assistant_id);
                }
            }

            $query = new WP_Query($args);
            $results = array();

            if ($query->have_posts()) {
                while ($query->have_posts()) {
                    $query->the_post();
                    $post_id = get_the_ID();
                    $post_embedding = get_post_meta($post_id, '_qkb_embedding', true);

                    if (empty($post_embedding) || !is_array($post_embedding)) {
                        continue;
                    }

                    // Calculate cosine similarity
                    $similarity = $this->calculate_cosine_similarity($query_embedding, $post_embedding);

                    if ($similarity >= $min_similarity) {
                        $results[] = array(
                            'id' => $post_id,
                            'title' => get_the_title(),
                            'content' => wp_strip_all_tags(get_the_content()),
                            'similarity' => $similarity,
                            'url' => get_permalink($post_id)
                        );
                    }
                }
            }
            wp_reset_postdata();

            // Sort by similarity (highest first)
            usort($results, function ($a, $b) {
                return $b['similarity'] <=> $a['similarity'];
            });

            // Limit results
            return array_slice($results, 0, $limit);

        } catch (Exception $e) {
            error_log('Error in get_similar_articles_by_embedding: ' . $e->getMessage());
            return array();
        }
    }

    /**
     * Calculate cosine similarity between two embeddings
     *
     * @param array $embedding1 First embedding vector
     * @param array $embedding2 Second embedding vector
     * @return float Similarity score (0-1)
     */
    private function calculate_cosine_similarity($embedding1, $embedding2)
    {
        if (empty($embedding1) || empty($embedding2) || count($embedding1) !== count($embedding2)) {
            return 0;
        }

        $dot_product = 0;
        $magnitude1 = 0;
        $magnitude2 = 0;

        foreach ($embedding1 as $i => $val1) {
            $val2 = $embedding2[$i];
            $dot_product += $val1 * $val2;
            $magnitude1 += $val1 * $val1;
            $magnitude2 += $val2 * $val2;
        }

        $magnitude1 = sqrt($magnitude1);
        $magnitude2 = sqrt($magnitude2);

        if ($magnitude1 == 0 || $magnitude2 == 0) {
            return 0;
        }

        return $dot_product / ($magnitude1 * $magnitude2);
    }
}
